<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LatestNewsTitle extends Model
{
    use HasFactory;

    protected $table = 'latest_news_title';

    protected $fillable = [
        'title',
        'country',
        'language',
        'source_published_at',
        'is_processed',
        'is_generated',
        'generated_news_id',
        'api_response',
    ];

    protected $casts = [
        'source_published_at' => 'datetime',
        'is_processed' => 'boolean',
        'is_generated' => 'boolean',
        'api_response' => 'array',
    ];

    /**
     * Get the generated news article.
     */
    public function generatedNews(): BelongsTo
    {
        return $this->belongsTo(News::class, 'generated_news_id');
    }

    /**
     * Scope for unprocessed news
     */
    public function scopeUnprocessed($query)
    {
        return $query->where('is_processed', false);
    }

    /**
     * Scope for processed but not generated news
     */
    public function scopeReadyForGeneration($query)
    {
        return $query->where('is_processed', true)
                    ->where('is_generated', false);
    }

    /**
     * Check if this news already exists based on title only
     */
    public static function exists($title)
    {
        return static::where('title', $title)->exists();
    }

    /**
     * Mark as processed
     */
    public function markAsProcessed()
    {
        $this->update(['is_processed' => true]);
    }

    /**
     * Mark as generated with the news ID
     */
    public function markAsGenerated($newsId)
    {
        $this->update([
            'is_generated' => true,
            'generated_news_id' => $newsId,
        ]);
    }
}
