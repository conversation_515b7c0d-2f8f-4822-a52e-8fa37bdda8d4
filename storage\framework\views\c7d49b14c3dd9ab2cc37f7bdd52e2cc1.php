    <title><?php echo e($meta_title); ?></title>
    <meta name="description" content="<?php echo e($meta_description); ?>">
    <meta name="keywords" content="<?php echo e($meta_keywords ?? 'news, breaking news, headlines'); ?>">
    <link rel="canonical" href="<?php echo e($canonical ?? url()->current()); ?>">
    <meta name="robots" content="<?php echo e($robots_meta ?? 'index,follow'); ?>">
    <meta property="og:title" content="<?php echo e($meta_title); ?>">
    <meta property="og:description" content="<?php echo e($meta_description); ?>">
    <meta property="og:image" content="<?php echo e($meta_image); ?>">
    <meta property="og:url" content="<?php echo e($canonical ?? url()->current()); ?>">
    <meta property="og:type" content="<?php echo e($og_type ?? 'website'); ?>">
    <meta property="og:site_name" content="<?php echo e(config('app.name', 'News')); ?>">
    <meta name="twitter:card" content="<?php echo e($twitter_card ?? 'summary_large_image'); ?>">
    <meta name="twitter:title" content="<?php echo e($twitter_title ?? $meta_title); ?>">
    <meta name="twitter:description" content="<?php echo e($twitter_description ?? $meta_description); ?>">
    <meta name="twitter:image" content="<?php echo e($twitter_image ?? $meta_image); ?>">
    <meta name="twitter:url" content="<?php echo e($canonical ?? url()->current()); ?>">
    <?php if(isset($isSingleNews)): ?>
    <meta property="article:published_time" content="<?php echo e($published_at); ?>">
    <meta property="article:modified_time" content="<?php echo e($updated_at); ?>">
    <meta property="article:author" content="LTNews">
    <meta property="article:section" content="<?php echo e($categories); ?>">
    <meta property="article:tag" content="<?php echo e($tags); ?>">
    <?php endif; ?>
    <meta name="theme-color" content="#ffffff">
    <link rel="icon" href="<?php echo e(asset('storage/images/logo-color.svg')); ?>" type="image/x-icon">
    <link rel="apple-touch-icon" href="<?php echo e(asset('storage/images/logo-color.svg')); ?>">
    <link rel="manifest" href="<?php echo e(asset('manifest.json')); ?>">
    <link rel="mask-icon" href="<?php echo e(asset('storage/images/logo-color.svg')); ?>" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="<?php echo e(asset('browserconfig.xml')); ?>">
    <meta name="google-site-verification" content="<?php echo e(config('services.google.site_verification')); ?>">
    <meta name="yandex-verification" content="<?php echo e(config('services.yandex.verification')); ?>">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="<?php echo e(config('app.name', 'News')); ?>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="preload" href="<?php echo e(asset('fonts/roboto/Roboto-Regular.woff2')); ?>" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo e(asset('fonts/roboto/Roboto-Medium.woff2')); ?>" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo e(asset('fonts/roboto/Roboto-Bold.woff2')); ?>" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo e(asset('fonts/roboto/Roboto-Italic.woff2')); ?>" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo e(asset('fonts/roboto/Roboto-MediumItalic.woff2')); ?>" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo e(asset('fonts/roboto/Roboto-BoldItalic.woff2')); ?>" as="font" type="font/woff2" crossorigin>
     <?php if(isset($featuredNews) && $featuredNews->isNotEmpty() && $featuredNews[0]->main_image): ?>
    <link rel="preload" as="image" href="<?php echo e(asset('storage/' . $featuredNews[0]->main_image)); ?>"  fetchpriority="high" type="image/svg+xml">
    <?php endif; ?><?php /**PATH C:\projects\client-admin-panel\resources\views/components/frontend/seo.blade.php ENDPATH**/ ?>