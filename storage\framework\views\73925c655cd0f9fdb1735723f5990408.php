<?php $__env->startSection('title', 'Settings'); ?>

<?php
    $pageTitle = 'Settings';
    $pageDescription = 'Manage your admin panel and site settings';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Settings', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <div class="flex flex-wrap gap-3">
        <form id="logout-all-devices-form" method="POST" action="<?php echo e(route('admin.settings.logout-all-devices')); ?>" class="inline">
            <?php echo csrf_field(); ?>
            <button type="button" id="logout-all-devices-btn" class="material-button material-button-md material-button-danger flex items-center">
                <i class="material-icons mr-2">logout</i>
                Logout MY Devices Only
            </button>
        </form>
        <form id="emergency-logout-form" method="POST" action="<?php echo e(route('admin.settings.emergency-logout-all')); ?>" class="inline">
            <?php echo csrf_field(); ?>
            <button type="button" id="emergency-logout-btn" class="material-button material-button-md bg-red-900 hover:bg-red-950 text-white flex items-center border-2 border-red-700">
                <i class="material-icons mr-2">power_settings_new</i>
                Emergency Logout ALL
            </button>
        </form>
        <form id="clear-cache-form" method="POST" action="<?php echo e(route('admin.settings.clear-cache')); ?>" class="inline">
            <?php echo csrf_field(); ?>
            <button type="button" id="clear-cache-btn" class="material-button material-button-md material-button-warning flex items-center">
                <i class="material-icons mr-2">cached</i>
                Clear Cache
            </button>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<form id="settings_form" method="POST" action="<?php echo e(route('admin.settings.update')); ?>" class="space-y-6">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>

    <!-- General Settings -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2">settings</i>
                General Settings
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Site Name -->
                <div>
                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Site Name
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">business</i>
                        </div>
                        <input type="text" 
                               id="site_name" 
                               name="site_name" 
                               value="<?php echo e(old('site_name', $settings['general']['site_name'] ?? '')); ?>" 
                               class="material-input-with-icon <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               placeholder="Enter site name">
                    </div>
                    <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Admin Email -->
                <div>
                    <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">
                        Admin Email
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">admin_panel_settings</i>
                        </div>
                        <input type="email" 
                               id="admin_email" 
                               name="admin_email" 
                               value="<?php echo e(old('admin_email', $settings['general']['admin_email'] ?? '')); ?>" 
                               class="material-input-with-icon <?php $__errorArgs = ['admin_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               placeholder="Enter admin email">
                    </div>
                    <?php $__errorArgs = ['admin_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Site Description -->
                <div class="lg:col-span-2">
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                        Site Description
                    </label>
                    <textarea id="site_description" 
                              name="site_description" 
                              rows="3"
                              class="material-textarea <?php $__errorArgs = ['site_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                              placeholder="Enter site description"><?php echo e(old('site_description', $settings['general']['site_description'] ?? '')); ?></textarea>
                    <?php $__errorArgs = ['site_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Timezone -->
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                        Timezone
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">schedule</i>
                        </div>
                        <select id="timezone" 
                                name="timezone" 
                                class="material-select-with-icon <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-select-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Select timezone</option>
                            <option value="UTC" <?php echo e(old('timezone', $settings['general']['timezone'] ?? '') == 'UTC' ? 'selected' : ''); ?>>UTC</option>
                            <option value="Asia/Kolkata" <?php echo e(old('timezone', $settings['general']['timezone'] ?? '') == 'Asia/Kolkata' ? 'selected' : ''); ?>>Asia/Kolkata (IST)</option>
                            <option value="America/New_York" <?php echo e(old('timezone', $settings['general']['timezone'] ?? '') == 'America/New_York' ? 'selected' : ''); ?>>America/New_York (EST)</option>
                            <option value="Europe/London" <?php echo e(old('timezone', $settings['general']['timezone'] ?? '') == 'Europe/London' ? 'selected' : ''); ?>>Europe/London (GMT)</option>
                        </select>
                    </div>
                    <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- SMTP Settings -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2">mail</i>
                SMTP Settings
                <span class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Encrypted</span>
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- SMTP Host -->
                <div>
                    <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Host
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">dns</i>
                        </div>
                        <input type="text" 
                               id="smtp_host" 
                               name="smtp_host" 
                               value="<?php echo e(old('smtp_host', $settings['smtp']['smtp_host'] ?? '')); ?>" 
                               class="material-input-with-icon <?php $__errorArgs = ['smtp_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               placeholder="smtp.gmail.com">
                    </div>
                    <?php $__errorArgs = ['smtp_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- SMTP Port -->
                <div>
                    <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Port
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">router</i>
                        </div>
                        <input type="number" 
                               id="smtp_port" 
                               name="smtp_port" 
                               value="<?php echo e(old('smtp_port', $settings['smtp']['smtp_port'] ?? '')); ?>" 
                               class="material-input-with-icon <?php $__errorArgs = ['smtp_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               placeholder="587">
                    </div>
                    <?php $__errorArgs = ['smtp_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- SMTP Username -->
                <div>
                    <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Username
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">person</i>
                        </div>
                        <input type="text" 
                               id="smtp_username" 
                               name="smtp_username" 
                               value="<?php echo e(old('smtp_username', $settings['smtp']['smtp_username'] ?? '')); ?>" 
                               class="material-input-with-icon <?php $__errorArgs = ['smtp_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               placeholder="<EMAIL>">
                    </div>
                    <?php $__errorArgs = ['smtp_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- SMTP Password -->
                <div>
                    <label for="smtp_password" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">lock</i>
                        </div>
                        <input type="password" 
                               id="smtp_password" 
                               name="smtp_password" 
                               value="<?php echo e(old('smtp_password', $settings['smtp']['smtp_password'] ?? '')); ?>" 
                               class="material-input-with-icon <?php $__errorArgs = ['smtp_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               placeholder="Your SMTP password">
                        <button type="button" 
                                id="toggle-smtp-password"
                                data-password-toggle="smtp_password:smtp-password-icon"
                                class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                            <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="smtp-password-icon">visibility</i>
                        </button>
                    </div>
                    <?php $__errorArgs = ['smtp_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- SMTP Encryption -->
                <div>
                    <label for="smtp_encryption" class="block text-sm font-medium text-gray-700 mb-2">
                        Encryption
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">security</i>
                        </div>
                        <select id="smtp_encryption" 
                                name="smtp_encryption" 
                                class="material-select-with-icon <?php $__errorArgs = ['smtp_encryption'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-select-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">No encryption</option>
                            <option value="tls" <?php echo e(old('smtp_encryption', $settings['smtp']['smtp_encryption'] ?? '') == 'tls' ? 'selected' : ''); ?>>TLS</option>
                            <option value="ssl" <?php echo e(old('smtp_encryption', $settings['smtp']['smtp_encryption'] ?? '') == 'ssl' ? 'selected' : ''); ?>>SSL</option>
                        </select>
                    </div>
                    <?php $__errorArgs = ['smtp_encryption'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Settings -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2">email</i>
                Email Settings
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Mail From Address -->
                <div>
                    <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">
                        From Email Address
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">alternate_email</i>
                        </div>
                        <input type="email"
                               id="mail_from_address"
                               name="mail_from_address"
                               value="<?php echo e(old('mail_from_address', $settings['email']['mail_from_address'] ?? '')); ?>"
                               class="material-input-with-icon <?php $__errorArgs = ['mail_from_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="<EMAIL>">
                    </div>
                    <?php $__errorArgs = ['mail_from_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Mail From Name -->
                <div>
                    <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-2">
                        From Name
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">badge</i>
                        </div>
                        <input type="text"
                               id="mail_from_name"
                               name="mail_from_name"
                               value="<?php echo e(old('mail_from_name', $settings['email']['mail_from_name'] ?? '')); ?>"
                               class="material-input-with-icon <?php $__errorArgs = ['mail_from_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Your Site Name">
                    </div>
                    <?php $__errorArgs = ['mail_from_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Test Section -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2">send</i>
                Test Email Configuration
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 items-end">
                <div>
                    <label for="test_email" class="block text-sm font-medium text-gray-700 mb-2">
                        Test Email Address
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">email</i>
                        </div>
                        <input type="email"
                               id="test_email"
                               name="test_email"
                               class="material-input-with-icon"
                               placeholder="Enter email to test">
                    </div>
                </div>
                <div>
                    <button type="button"
                            onclick="sendTestEmail()"
                            class="material-button material-button-md material-button-secondary flex items-center w-full lg:w-auto">
                        <i class="material-icons mr-2">send</i>
                        Send Test Email
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Settings -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2">api</i>
                API Settings
                <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Encrypted</span>
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 gap-6">
                <!-- Gemini API Key -->
                <div>
                    <label for="gemini_api_key" class="block text-sm font-medium text-gray-700 mb-2">
                        Gemini AI API Key
                        <span class="text-xs text-gray-500 ml-1">(Updates both database and .env file)</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">smart_toy</i>
                        </div>
                        <input type="password"
                               id="gemini_api_key"
                               name="gemini_api_key"
                               value="<?php echo e(old('gemini_api_key', $settings['api']['gemini_api_key'] ?? '')); ?>"
                               class="material-input-with-icon <?php $__errorArgs = ['gemini_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter your Gemini API key">
                        <button type="button"
                                id="toggle-gemini-api-key"
                                data-password-toggle="gemini_api_key:gemini-api-key-icon"
                                class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                            <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="gemini-api-key-icon">visibility</i>
                        </button>
                    </div>
                    <?php $__errorArgs = ['gemini_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <p class="mt-1 text-xs text-gray-500">
                        This key will be saved encrypted in the database and also updated in your .env file for immediate use.
                    </p>
                </div>

                <!-- Test Gemini API Key -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 items-end">
                    <div>
                        <label for="test_gemini_key" class="block text-sm font-medium text-gray-700 mb-2">
                            Test API Key
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">smart_toy</i>
                            </div>
                            <input type="password"
                                   id="test_gemini_key"
                                   name="test_gemini_key"
                                   class="material-input-with-icon"
                                   placeholder="Enter API key to test">
                            <button type="button"
                                    id="toggle-test-gemini-key"
                                    data-password-toggle="test_gemini_key:test-gemini-key-icon"
                                    class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                                <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="test-gemini-key-icon">visibility</i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <button type="button"
                                onclick="testGeminiApi()"
                                class="material-button material-button-md material-button-secondary flex items-center w-full lg:w-auto">
                            <i class="material-icons mr-2">science</i>
                            Test API Key
                        </button>
                    </div>
                </div>

                <!-- API Key Status -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="material-icons text-gray-600 mr-2">info</i>
                        <h4 class="font-medium text-gray-900">API Key Status</h4>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Database:</span>
                            <span class="text-xs px-2 py-1 rounded-full <?php echo e(isset($settings['api']['gemini_api_key']) && !empty($settings['api']['gemini_api_key']) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e(isset($settings['api']['gemini_api_key']) && !empty($settings['api']['gemini_api_key']) ? 'Configured' : 'Not Set'); ?>

                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">.env File:</span>
                            <?php
                                $envGeminiKey = \App\Services\EnvManager::getEnvValue('GEMINI_API_KEY');
                            ?>
                            <span class="text-xs px-2 py-1 rounded-full <?php echo e($envGeminiKey ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e($envGeminiKey ? 'Configured' : 'Not Set'); ?>

                            </span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-3">
                        Both locations should be configured for the API to work properly. The .env value is used by the application at runtime.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Section -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2">security</i>
                Security & Sessions
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Active Sessions Info -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="material-icons text-blue-600 mr-2">devices</i>
                        <h4 class="font-medium text-blue-900">Active Sessions</h4>
                    </div>
                    <?php
                        $activeSessions = \Illuminate\Support\Facades\DB::table('sessions')
                            ->where('user_id', auth()->id())
                            ->get(['id', 'ip_address', 'user_agent', 'last_activity']);
                        $currentSessionId = session()->getId();
                    ?>
                    <p class="text-sm text-blue-700 mb-3">
                        You are currently logged in on
                        <span class="font-semibold"><?php echo e($activeSessions->count()); ?></span> device(s).
                    </p>

                    <?php if($activeSessions->count() > 0): ?>
                        <div class="mt-3 space-y-2">
                            <p class="text-xs font-medium text-blue-800">Session Details:</p>
                            <?php $__currentLoopData = $activeSessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="text-xs text-blue-600 bg-blue-100 rounded px-2 py-1">
                                    <div class="flex items-center justify-between">
                                        <span>
                                            <?php echo e($session->ip_address); ?>

                                            <?php if($session->id === $currentSessionId): ?>
                                                <span class="text-green-600 font-medium">(Current)</span>
                                            <?php endif; ?>
                                        </span>
                                        <span><?php echo e(\Carbon\Carbon::createFromTimestamp($session->last_activity)->diffForHumans()); ?></span>
                                    </div>
                                    <?php if($session->user_agent): ?>
                                        <div class="text-blue-500 truncate mt-1">
                                            <?php echo e(Str::limit($session->user_agent, 50)); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>

                    <p class="text-xs text-blue-600 mt-3">
                        This includes your current session and any other browsers or devices where you're logged in.
                    </p>
                </div>

                <!-- Logout All Devices Info -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i class="material-icons text-red-600 mr-2">logout</i>
                        <h4 class="font-medium text-red-900">Logout Options</h4>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm font-medium text-blue-800">Logout MY Devices Only:</p>
                            <p class="text-xs text-blue-600">
                                ✅ Logs out only YOUR sessions from all YOUR devices. Other users are NOT affected.
                            </p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-red-900">Emergency Logout ALL:</p>
                            <p class="text-xs text-red-700">
                                🚨 CLEARS ENTIRE SESSIONS TABLE - Logs out ALL users from ALL devices. Only you remain logged in!
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4">
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">cancel</i>
            Cancel
        </a>
        <button type="submit" class="material-button material-button-md material-button-primary flex items-center">
            <i class="material-icons mr-2">save</i>
            Save Settings
        </button>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Custom SweetAlert button styles to match material design */
    .swal-confirm-btn {
        background-color: #f59e0b !important;
        color: white !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    }

    .swal-confirm-btn:hover {
        background-color: #d97706 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    .swal-cancel-btn {
        background-color: #6b7280 !important;
        color: white !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
        margin-right: 0.75rem !important;
    }

    .swal-cancel-btn:hover {
        background-color: #4b5563 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    /* SweetAlert container styling */
    .swal2-popup {
        border-radius: 0.75rem !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    }

    .swal2-title {
        font-weight: 600 !important;
        color: #1f2937 !important;
    }

    .swal2-content {
        color: #6b7280 !important;
    }

    /* Success button styling */
    .swal-success-btn {
        background-color: #10b981 !important;
        color: white !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    }

    .swal-success-btn:hover {
        background-color: #059669 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    /* Error button styling */
    .swal-error-btn {
        background-color: #ef4444 !important;
        color: white !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    }

    .swal-error-btn:hover {
        background-color: #dc2626 !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    /* Primary button styling */
    .swal-primary-btn {
        background-color: #3b82f6 !important;
        color: white !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    }

    .swal-primary-btn:hover {
        background-color: #2563eb !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    /* Nuclear button styling */
    .swal-nuclear-btn {
        background-color: #7f1d1d !important;
        color: white !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
        text-transform: uppercase !important;
        letter-spacing: 0.05em !important;
    }

    .swal-nuclear-btn:hover {
        background-color: #450a0a !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        transform: translateY(-1px) !important;
    }

    /* Emergency button styling */
    .swal-emergency-btn {
        background-color: #7f1d1d !important;
        color: white !important;
        border: 2px solid #991b1b !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 700 !important;
        font-size: 0.875rem !important;
        display: inline-flex !important;
        align-items: center !important;
        transition: all 0.2s !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        text-transform: uppercase !important;
        letter-spacing: 0.05em !important;
        animation: pulse 2s infinite !important;
    }

    .swal-emergency-btn:hover {
        background-color: #450a0a !important;
        border-color: #7f1d1d !important;
        box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        transform: translateY(-2px) !important;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.8;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<?php echo app('Illuminate\Foundation\Vite')('resources/js/utils/common.js'); ?>
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // Initialize password toggle
        initPasswordToggle('toggle-smtp-password', 'smtp_password', 'smtp-password-icon');
        initPasswordToggle('toggle-gemini-api-key', 'gemini_api_key', 'gemini-api-key-icon');
        initPasswordToggle('toggle-test-gemini-key', 'test_gemini_key', 'test-gemini-key-icon');

        // Clear Cache SweetAlert
        $('#clear-cache-btn').on('click', function(e) {
            e.preventDefault();

            Swal.fire({
                title: 'Clear All Caches?',
                text: 'This will clear application cache, config cache, route cache, view cache, and settings cache.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#f59e0b',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cached</i>Yes, Clear Cache',
                cancelButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cancel</i>Cancel',
                customClass: {
                    confirmButton: 'swal-confirm-btn',
                    cancelButton: 'swal-cancel-btn'
                },
                buttonsStyling: false,
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Clearing Cache...',
                        text: 'Please wait while we clear all caches.',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit the form
                    $('#clear-cache-form').submit();
                }
            });
        });

        // Logout All Devices SweetAlert
        $('#logout-all-devices-btn').on('click', function(e) {
            e.preventDefault();

            Swal.fire({
                title: 'Logout All MY Devices Only?',
                html: `
                    <div class="text-left">
                        <p class="mb-3 font-medium text-blue-800">This will log YOU out from:</p>
                        <ul class="list-disc list-inside text-sm mb-3 text-gray-700 bg-blue-50 p-3 rounded">
                            <li>All YOUR other browser tabs</li>
                            <li>All YOUR incognito/private tabs</li>
                            <li>All YOUR mobile devices</li>
                            <li>All YOUR other computers</li>
                        </ul>
                        <p class="text-green-600 font-medium">✅ Your current session will remain active.</p>
                        <p class="text-green-600 font-medium">✅ Other users will NOT be affected.</p>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">logout</i>Yes, Logout My Devices',
                cancelButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cancel</i>Cancel',
                customClass: {
                    confirmButton: 'swal-error-btn',
                    cancelButton: 'swal-cancel-btn'
                },
                buttonsStyling: false,
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Logging Out...',
                        text: 'Please wait while we logout from all other devices.',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit the form
                    $('#logout-all-devices-form').submit();
                }
            });
        });

        // Emergency Logout ALL SweetAlert (EXTREME WARNING)
        $('#emergency-logout-btn').on('click', function(e) {
            e.preventDefault();

            Swal.fire({
                title: '🚨 EMERGENCY LOGOUT ALL USERS! 🚨',
                html: `
                    <div class="text-left">
                        <p class="text-red-600 font-bold mb-3 text-center">⚠️ EXTREME EMERGENCY ACTION ⚠️</p>
                        <p class="mb-3 text-center"><strong>This will IMMEDIATELY logout ALL users from EVERYTHING:</strong></p>
                        <ul class="list-disc list-inside text-sm mb-4 text-gray-700 bg-red-50 p-3 rounded">
                            <li>ALL admin users from ALL devices</li>
                            <li>ALL browser tabs (regular + incognito)</li>
                            <li>ALL mobile devices and computers</li>
                            <li>CLEARS entire sessions table</li>
                            <li>ONLY YOU will remain logged in</li>
                        </ul>
                        <p class="text-red-600 font-medium text-center">Use only in security emergencies!</p>
                    </div>
                `,
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#7f1d1d',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">power_settings_new</i>YES, EMERGENCY LOGOUT ALL',
                cancelButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cancel</i>Cancel',
                customClass: {
                    confirmButton: 'swal-emergency-btn',
                    cancelButton: 'swal-cancel-btn'
                },
                buttonsStyling: false,
                reverseButtons: true,
                focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Triple confirmation for emergency action
                    Swal.fire({
                        title: 'FINAL CONFIRMATION',
                        html: `
                            <div class="text-center">
                                <p class="text-red-700 font-bold mb-3">⚠️ LAST CHANCE TO CANCEL ⚠️</p>
                                <p class="mb-3">This will <strong>CLEAR THE ENTIRE SESSIONS TABLE</strong></p>
                                <p class="text-red-600">ALL users will be logged out IMMEDIATELY!</p>
                                <p class="text-sm text-gray-600 mt-3">This action cannot be undone.</p>
                            </div>
                        `,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#7f1d1d',
                        cancelButtonColor: '#6b7280',
                        confirmButtonText: 'EXECUTE EMERGENCY LOGOUT',
                        cancelButtonText: 'Cancel',
                        customClass: {
                            confirmButton: 'swal-emergency-btn',
                            cancelButton: 'swal-cancel-btn'
                        },
                        buttonsStyling: false,
                        reverseButtons: true
                    }).then((finalResult) => {
                        if (finalResult.isConfirmed) {
                            // Show loading state
                            Swal.fire({
                                title: '🚨 EMERGENCY LOGOUT IN PROGRESS...',
                                text: 'Clearing entire sessions table and logging out all users...',
                                icon: 'warning',
                                allowOutsideClick: false,
                                allowEscapeKey: false,
                                showConfirmButton: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });

                            // Submit the emergency form
                            $('#emergency-logout-form').submit();
                        }
                    });
                }
            });
        });

        // Nuclear Logout SweetAlert (EXTREME WARNING)
        $('#nuclear-logout-btn').on('click', function(e) {
            e.preventDefault();

            Swal.fire({
                title: '🚨 NUCLEAR LOGOUT WARNING! 🚨',
                html: `
                    <div class="text-left">
                        <p class="text-red-600 font-bold mb-3">⚠️ EXTREME SECURITY MEASURE ⚠️</p>
                        <p class="mb-2">This will <strong>IMMEDIATELY LOGOUT ALL USERS</strong> from:</p>
                        <ul class="list-disc list-inside text-sm mb-3 text-gray-700">
                            <li>All regular browser tabs</li>
                            <li>All incognito/private tabs</li>
                            <li>All mobile devices</li>
                            <li>All other computers</li>
                            <li><strong>ALL OTHER ADMIN USERS</strong></li>
                        </ul>
                        <p class="text-red-600 font-medium">Use only if you suspect a security breach!</p>
                    </div>
                `,
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#7f1d1d',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">warning</i>YES, NUCLEAR LOGOUT',
                cancelButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cancel</i>Cancel',
                customClass: {
                    confirmButton: 'swal-nuclear-btn',
                    cancelButton: 'swal-cancel-btn'
                },
                buttonsStyling: false,
                reverseButtons: true,
                focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Double confirmation for nuclear option
                    Swal.fire({
                        title: 'Are you ABSOLUTELY sure?',
                        text: 'This will log out EVERYONE including other administrators. This action cannot be undone.',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#7f1d1d',
                        cancelButtonColor: '#6b7280',
                        confirmButtonText: 'YES, DO IT NOW',
                        cancelButtonText: 'No, Cancel',
                        customClass: {
                            confirmButton: 'swal-nuclear-btn',
                            cancelButton: 'swal-cancel-btn'
                        },
                        buttonsStyling: false,
                        reverseButtons: true
                    }).then((finalResult) => {
                        if (finalResult.isConfirmed) {
                            // Show loading state
                            Swal.fire({
                                title: '🚨 NUCLEAR LOGOUT IN PROGRESS...',
                                text: 'Terminating all sessions from all users...',
                                icon: 'warning',
                                allowOutsideClick: false,
                                allowEscapeKey: false,
                                showConfirmButton: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });

                            // Submit the nuclear form
                            $('#nuclear-logout-form').submit();
                        }
                    });
                }
            });
        });

        // Handle flash messages with SweetAlert
        <?php if(session('success')): ?>
            Swal.fire({
                title: 'Success!',
                text: '<?php echo e(session('success')); ?>',
                icon: 'success',
                confirmButtonColor: '#10b981',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">check</i>OK',
                customClass: {
                    confirmButton: 'swal-success-btn'
                },
                buttonsStyling: false
            });
        <?php endif; ?>

        <?php if(session('error')): ?>
            Swal.fire({
                title: 'Error!',
                text: '<?php echo e(session('error')); ?>',
                icon: 'error',
                confirmButtonColor: '#ef4444',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">close</i>OK',
                customClass: {
                    confirmButton: 'swal-error-btn'
                },
                buttonsStyling: false
            });
        <?php endif; ?>

        // Form validation
        $("#settings_form").validate({
            rules: {
                site_name: {
                    maxlength: 255
                },
                site_description: {
                    maxlength: 500
                },
                admin_email: {
                    email: true,
                    maxlength: 255
                },
                smtp_host: {
                    maxlength: 255
                },
                smtp_port: {
                    digits: true,
                    min: 1,
                    max: 65535
                },
                smtp_username: {
                    maxlength: 255
                },
                smtp_password: {
                    maxlength: 255
                },
                mail_from_address: {
                    email: true,
                    maxlength: 255
                },
                mail_from_name: {
                    maxlength: 255
                },
                gemini_api_key: {
                    maxlength: 255
                }
            },
            messages: {
                admin_email: {
                    email: "Please enter a valid email address"
                },
                smtp_port: {
                    digits: "Please enter a valid port number",
                    min: "Port must be at least 1",
                    max: "Port cannot exceed 65535"
                },
                mail_from_address: {
                    email: "Please enter a valid email address"
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative').length ? element.closest('.relative') : element);
            },
            highlight: function (element) {
                $(element).addClass('material-input-error border-red-500').removeClass('border-gray-300');
            },
            unhighlight: function (element) {
                $(element).removeClass('material-input-error border-red-500').addClass('border-gray-300');
            },
            submitHandler: function(form) {
                // Show loading state
                const submitBtn = $(form).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.prop('disabled', true).html('<i class="material-icons mr-2">hourglass_empty</i>Saving...');

                // Submit the form
                form.submit();
            }
        });
    });

    // Send test email function with SweetAlert
    function sendTestEmail() {
        const testEmail = document.getElementById('test_email').value;

        if (!testEmail) {
            Swal.fire({
                title: 'Email Required',
                text: 'Please enter a test email address',
                icon: 'warning',
                confirmButtonColor: '#f59e0b',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">check</i>OK',
                customClass: {
                    confirmButton: 'swal-confirm-btn'
                },
                buttonsStyling: false
            });
            return;
        }

        if (!testEmail.includes('@')) {
            Swal.fire({
                title: 'Invalid Email',
                text: 'Please enter a valid email address',
                icon: 'error',
                confirmButtonColor: '#ef4444',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">check</i>OK',
                customClass: {
                    confirmButton: 'swal-error-btn'
                },
                buttonsStyling: false
            });
            return;
        }

        // Confirm before sending
        Swal.fire({
            title: 'Send Test Email?',
            text: `Send a test email to ${testEmail}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">send</i>Send Email',
            cancelButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cancel</i>Cancel',
            customClass: {
                confirmButton: 'swal-primary-btn',
                cancelButton: 'swal-cancel-btn'
            },
            buttonsStyling: false,
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Sending Test Email...',
                    text: 'Please wait while we send the test email.',
                    icon: 'info',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Create a form to send test email
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '<?php echo e(route("admin.settings.test-email")); ?>';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '<?php echo e(csrf_token()); ?>';

                const emailInput = document.createElement('input');
                emailInput.type = 'hidden';
                emailInput.name = 'test_email';
                emailInput.value = testEmail;

                form.appendChild(csrfToken);
                form.appendChild(emailInput);
                document.body.appendChild(form);

                form.submit();
            }
        });
    }

    // Test Gemini API function with SweetAlert
    function testGeminiApi() {
        const testApiKey = document.getElementById('test_gemini_key').value;

        if (!testApiKey) {
            Swal.fire({
                title: 'API Key Required',
                text: 'Please enter a Gemini API key to test',
                icon: 'warning',
                confirmButtonColor: '#f59e0b',
                confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">check</i>OK',
                customClass: {
                    confirmButton: 'swal-confirm-btn'
                },
                buttonsStyling: false
            });
            return;
        }

        // Confirm before testing
        Swal.fire({
            title: 'Test Gemini API Key?',
            text: 'This will validate the API key format and basic connectivity.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">science</i>Test API Key',
            cancelButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">cancel</i>Cancel',
            customClass: {
                confirmButton: 'swal-primary-btn',
                cancelButton: 'swal-cancel-btn'
            },
            buttonsStyling: false,
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Testing API Key...',
                    text: 'Please wait while we validate your Gemini API key.',
                    icon: 'info',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Make AJAX request to test API key
                fetch('<?php echo e(route("admin.settings.test-gemini-api")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({
                        api_key: testApiKey
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'API Key Test Successful!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonColor: '#10b981',
                            confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">check</i>OK',
                            customClass: {
                                confirmButton: 'swal-success-btn'
                            },
                            buttonsStyling: false
                        });
                    } else {
                        Swal.fire({
                            title: 'API Key Test Failed',
                            text: data.message,
                            icon: 'error',
                            confirmButtonColor: '#ef4444',
                            confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">close</i>OK',
                            customClass: {
                                confirmButton: 'swal-error-btn'
                            },
                            buttonsStyling: false
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        title: 'Test Error',
                        text: 'An error occurred while testing the API key: ' + error.message,
                        icon: 'error',
                        confirmButtonColor: '#ef4444',
                        confirmButtonText: '<i class="material-icons mr-2" style="font-size: 16px; vertical-align: middle;">close</i>OK',
                        customClass: {
                            confirmButton: 'swal-error-btn'
                        },
                        buttonsStyling: false
                    });
                });
            }
        });
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>