@extends('layouts.admin')

@section('title', 'Add New API Key')

@section('content')
<!-- <PERSON> Header -->
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8">
    <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Add New API Key</h1>
        <p class="text-gray-600">Create a new API key for your services</p>
    </div>
    <div class="flex items-center space-x-3 mt-4 sm:mt-0">
        <a href="{{ route('admin.api-keys.index') }}" 
           class="material-button material-button-secondary">
            <i class="material-icons text-sm mr-2">arrow_back</i>
            Back to API Keys
        </a>
    </div>
</div>

<!-- Create Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">API Key Information</h3>
        <p class="mt-1 text-sm text-gray-600">Enter the details for your new API key</p>
    </div>

    <div class="p-6">
        <form id="api_key_form" action="{{ route('admin.api-keys.store') }}" method="POST">
            @csrf

            <!-- Form Layout - Responsive Grid -->
            <div class="space-y-6">
                <!-- Row 1: Provider and Status -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Provider Field -->
                    <div class="order-1 lg:order-1">
                        <label for="author" class="block text-sm font-medium text-gray-700 mb-2">
                            API Provider <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">business</i>
                            </div>
                            <select id="author" name="author" required
                                class="material-input-with-icon @error('author') material-input-error @enderror">
                                <option value="">Select API Provider</option>
                                @foreach($providers as $key => $name)
                                    <option value="{{ $key }}" {{ old('author') == $key ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('author')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status Field -->
                    <div class="order-2 lg:order-2">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">toggle_on</i>
                            </div>
                            <select id="status" name="status" required
                                class="material-input-with-icon @error('status') material-input-error @enderror">
                                <option value="">Select Status</option>
                                <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 2: API Key -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="key" class="block text-sm font-medium text-gray-700 mb-2">
                            API Key <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">vpn_key</i>
                            </div>
                            <textarea id="key" name="key" rows="3" required
                                class="material-input-with-icon @error('key') material-input-error @enderror"
                                placeholder="Enter your API key">{{ old('key') }}</textarea>
                        </div>
                        @error('key')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            <i class="material-icons text-xs mr-1">security</i>
                            Your API key will be encrypted and stored securely
                        </p>
                    </div>
                </div>

                <!-- Row 3: Currently Used -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <div class="flex items-center">
                            <input id="is_active" name="is_active" type="checkbox" value="1"
                                {{ old('is_active') ? 'checked' : '' }}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Set as currently used key for this provider
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            If checked, this key will be used for API calls and other keys for the same provider will be deactivated
                        </p>
                        @error('is_active')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 mt-8">
                <a href="{{ route('admin.api-keys.index') }}" 
                   class="material-button material-button-secondary">
                    Cancel
                </a>
                <button type="submit" class="material-button material-button-primary">
                    <i class="material-icons text-sm mr-2">save</i>
                    Create API Key
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<!-- jQuery Validation -->
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>

<script>
$(document).ready(function() {
    // jQuery Form validation
    $("#api_key_form").validate({
        rules: {
            author: {
                required: true
            },
            key: {
                required: true,
                minlength: 10,
                maxlength: 1000
            },
            status: {
                required: true
            }
        },
        messages: {
            author: {
                required: "Please select an API provider"
            },
            key: {
                required: "Please enter the API key",
                minlength: "API key must be at least 10 characters long",
                maxlength: "API key cannot exceed 1000 characters"
            },
            status: {
                required: "Please select a status"
            }
        },
        errorElement: 'p',
        errorClass: 'mt-1 text-sm text-red-600',
        highlight: function(element) {
            $(element).addClass('material-input-error');
        },
        unhighlight: function(element) {
            $(element).removeClass('material-input-error');
        },
        errorPlacement: function(error, element) {
            error.insertAfter(element.closest('.relative'));
        }
    });

    // Auto-resize textarea
    $('#key').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@endpush
