<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;
use Carbon\Carbon;

class SearchController extends Controller
{
    public function search(Request $request)
    {
        $query = $request->input('query');

        if (empty($query)) {
            return response()->json([
                'html' => '',
                'count' => 0,
                'total' => 0
            ]);
        }

        // Get total count for all matching articles
        $totalCount = News::published()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->count();

        // Search for news articles that match the query (limited to 10)
        $articles = News::published()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->with('categories') // Eager load the categories relationship
            ->orderBy('published_at', 'desc')
            ->limit(10)
            ->get();

        // Generate HTML view
        $html = view('frontend.partials.search-results', [
            'articles' => $articles,
            'query' => $query,
            'totalCount' => $totalCount
        ])->render();

        return response()->json([
            'html' => $html,
            'count' => $articles->count(),
            'total' => $totalCount
        ]);
    }

    public function searchPage(Request $request)
    {
        $query = $request->input('query', '');

        if (empty($query)) {
            return redirect()->route('home');
        }

        // Base query for search
        $baseQuery = News::published()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhere('meta_description', 'like', "%{$query}%");
            });

        // Count total matches (before limiting)
        $totalCount = $baseQuery->count();
        $hasMore = $totalCount > 10;

        // Get initial search results with limit
        $articles = (clone $baseQuery)
            ->with('categories')
            ->orderBy('published_at', 'desc')
            ->limit(10)
            ->get();

        $meta_title = "Search results for '{$query}' | " . config('app.name');
        $meta_description = "Find the latest results for '{$query}' on " . config('app.name');
        $meta_keywords = $query;
        $canonical = url()->current();
        $og_title = $meta_title;
        $og_description = $meta_description;
        $og_type = 'website';
        $meta_image = asset('default-image.jpg'); // Optional: set a fallback image
        $published_at = now()->format('Y-m-d\TH:i:sP');
        $updated_at = now()->format('Y-m-d\TH:i:sP');
        $robots_meta = 'noindex,follow';
        return view('frontend.search', compact(
            'articles',
            'query',
            'hasMore',
            'meta_title',
            'meta_description',
            'meta_keywords',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'meta_image',
            'published_at',
            'updated_at',
            'robots_meta'
        ));
    }

    public function loadMoreResults(Request $request)
    {
        $query = $request->input('query', '');
        $offset = (int) $request->input('offset', 0);
        $limit = (int) $request->input('limit', 10);

        if (empty($query)) {
            return response()->json([
                'html' => '',
                'hasMore' => false
            ]);
        }

        // Common search query builder
        $baseQuery = News::published()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhere('meta_description', 'like', "%{$query}%");
            });

        // Get the current batch of articles
        $moreArticles = (clone $baseQuery)
            ->with('categories')
            ->orderBy('published_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();

        // Check if more records exist beyond the current batch
        $hasMore = (clone $baseQuery)
            ->skip($offset + $limit)
            ->take(1)
            ->exists();

        $html = view('frontend.partials.search-results-items', [
            'articles' => $moreArticles
        ])->render();

        return response()->json([
            'html' => $html,
            'hasMore' => $hasMore
        ]);
    }
}
