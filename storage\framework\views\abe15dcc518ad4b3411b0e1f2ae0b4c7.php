<?php $__env->startSection('page-header'); ?>
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Login Logs & Security</h1>
            <p class="text-gray-600 mt-1">Monitor login attempts, failed logins, and account security</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="refreshPage()" class="material-button material-button-md material-button-secondary flex items-center">
                <i class="material-icons mr-2">refresh</i>
                Refresh
            </button>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Security Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="material-card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="material-icons text-red-600 text-lg">error</i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Failed Logins</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_failed']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="material-card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="material-icons text-yellow-600 text-lg">lock</i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Locked Accounts</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['locked_accounts']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="material-card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="material-icons text-gray-600 text-lg">block</i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Deactivated</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['deactivated_accounts']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="material-card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="material-icons text-blue-600 text-lg">email</i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Unique Emails</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['unique_emails']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="material-card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="material-icons text-purple-600 text-lg">location_on</i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Unique IPs</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['unique_ips']); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="material-card mb-6">
        <div class="p-6">
            <form method="GET" action="<?php echo e(route('admin.login-logs.index')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Log Type</label>
                    <select name="type" id="type" class="material-input">
                        <option value="all" <?php echo e($type === 'all' ? 'selected' : ''); ?>>All Types</option>
                        <option value="success" <?php echo e($type === 'success' ? 'selected' : ''); ?>>Successful</option>
                        <option value="failed" <?php echo e($type === 'failed' ? 'selected' : ''); ?>>Failed</option>
                        <option value="locked" <?php echo e($type === 'locked' ? 'selected' : ''); ?>>Locked</option>
                        <option value="deactivated" <?php echo e($type === 'deactivated' ? 'selected' : ''); ?>>Deactivated</option>
                    </select>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="text" name="email" id="email" value="<?php echo e($email); ?>" 
                           class="material-input" placeholder="Search by email...">
                </div>

                <div>
                    <label for="hours" class="block text-sm font-medium text-gray-700 mb-2">Time Range</label>
                    <select name="hours" id="hours" class="material-input">
                        <option value="1" <?php echo e($hours == 1 ? 'selected' : ''); ?>>Last Hour</option>
                        <option value="24" <?php echo e($hours == 24 ? 'selected' : ''); ?>>Last 24 Hours</option>
                        <option value="168" <?php echo e($hours == 168 ? 'selected' : ''); ?>>Last Week</option>
                        <option value="720" <?php echo e($hours == 720 ? 'selected' : ''); ?>>Last Month</option>
                        <option value="" <?php echo e($hours === null ? 'selected' : ''); ?>>All Time</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" class="material-button material-button-md material-button-primary flex items-center w-full">
                        <i class="material-icons mr-2">search</i>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Locked Accounts Section -->
    <?php if($lockedAttempts->count() > 0 || $deactivatedUsers->count() > 0): ?>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Currently Locked -->
            <?php if($lockedAttempts->count() > 0): ?>
                <div class="material-card">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="material-icons mr-2 text-yellow-600">lock</i>
                            Currently Locked (<?php echo e($lockedAttempts->count()); ?>)
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <?php $__currentLoopData = $lockedAttempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900"><?php echo e($attempt->email); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo e($attempt->ip_address); ?></p>
                                        <p class="text-xs text-yellow-700">
                                            <i class="material-icons text-xs mr-1">schedule</i>
                                            Unlocks in <?php echo e($attempt->remaining_lockout_time); ?>

                                        </p>
                                    </div>
                                    <form method="POST" action="<?php echo e(route('admin.login-logs.clear-attempts', $attempt->email)); ?>" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="material-button material-button-sm material-button-warning">
                                            <i class="material-icons">lock_open</i>
                                        </button>
                                    </form>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Deactivated Accounts -->
            <?php if($deactivatedUsers->count() > 0): ?>
                <div class="material-card">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="material-icons mr-2 text-red-600">block</i>
                            Deactivated Accounts (<?php echo e($deactivatedUsers->count()); ?>)
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <?php $__currentLoopData = $deactivatedUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900"><?php echo e($user->email); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo e($user->name); ?></p>
                                        <p class="text-xs text-red-700">
                                            Deactivated <?php echo e($user->deactivated_at?->diffForHumans()); ?>

                                        </p>
                                    </div>
                                    <form method="POST" action="<?php echo e(route('admin.login-logs.reactivate', $user->email)); ?>" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="material-button material-button-sm material-button-success">
                                            <i class="material-icons">check_circle</i>
                                        </button>
                                    </form>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Login Logs Table -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Login Logs</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Browser</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($log->created_at->format('M j, Y H:i:s')); ?>

                                <div class="text-xs text-gray-500"><?php echo e($log->created_at->diffForHumans()); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($log->email); ?></div>
                                <?php if($log->user): ?>
                                    <div class="text-sm text-gray-500"><?php echo e($log->user->name); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($log->type_badge_color); ?>">
                                    <?php echo e(ucfirst($log->type)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($log->ip_address); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($log->formatted_user_agent); ?></td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <?php echo e($log->failure_reason ?? '-'); ?>

                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <i class="material-icons text-4xl text-gray-300 mb-4">search_off</i>
                                <p>No login logs found for the selected criteria.</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($logs->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($logs->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>

    <script>
        function refreshPage() {
            window.location.reload();
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/login-logs/index.blade.php ENDPATH**/ ?>