<?php

namespace App\Helpers;

use App\Models\Category;

/**
 * ViewHelpers class provides utility functions for views
 *
 * Usage examples:
 *
 * 1. Check if category exists:
 *    ViewHelpers::categoryExists('business') // returns true/false
 *
 * 2. Get category object:
 *    ViewHelpers::getCategory('business') // returns Category model or null
 *
 * 3. In Blade templates:
 *    @categoryExists('business')
 *        <a href="/category/business">Business</a>
 *    @endcategoryExists
 *
 * 4. Navigation menu items (automatically used in header):
 *    ViewHelpers::getNavigationMenuItems() // returns array of menu items with existence status
 *
 * 5. Pages dropdown items:
 *    ViewHelpers::getPagesDropdownItems() // returns array of page menu items for dropdown
 *
 * Menu item structure:
 * - 'type' => 'link' (regular menu item) or 'dropdown' (dropdown menu)
 * - 'dropdown_items' => array of dropdown items (only for dropdown type)
 */
class ViewHelpers
{
    /**
     * Generate random color classes for categories with dark mode support
     * 
     * @return array Array with text and hover color classes
     */
    public static function getRandomCategoryColor()
    {
        $colors = [
            ['text' => 'text-red-700 dark:text-red-400', 'hover' => 'hover:text-red-600 dark:hover:text-red-300'],
            ['text' => 'text-blue-700 dark:text-blue-400', 'hover' => 'hover:text-blue-600 dark:hover:text-blue-300'],
            ['text' => 'text-green-700 dark:text-green-400', 'hover' => 'hover:text-green-600 dark:hover:text-green-300'],
            ['text' => 'text-purple-700 dark:text-purple-400', 'hover' => 'hover:text-purple-600 dark:hover:text-purple-300'],
            ['text' => 'text-indigo-700 dark:text-indigo-400', 'hover' => 'hover:text-indigo-600 dark:hover:text-indigo-300'],
            ['text' => 'text-pink-700 dark:text-pink-400', 'hover' => 'hover:text-pink-600 dark:hover:text-pink-300'],
            ['text' => 'text-yellow-700 dark:text-yellow-400', 'hover' => 'hover:text-yellow-600 dark:hover:text-yellow-300'],
            ['text' => 'text-teal-700 dark:text-teal-400', 'hover' => 'hover:text-teal-600 dark:hover:text-teal-300'],
            ['text' => 'text-orange-700 dark:text-orange-400', 'hover' => 'hover:text-orange-600 dark:hover:text-orange-300'],
            ['text' => 'text-cyan-700 dark:text-cyan-400', 'hover' => 'hover:text-cyan-600 dark:hover:text-cyan-300'],
            ['text' => 'text-emerald-700 dark:text-emerald-400', 'hover' => 'hover:text-emerald-600 dark:hover:text-emerald-300'],
            ['text' => 'text-violet-700 dark:text-violet-400', 'hover' => 'hover:text-violet-600 dark:hover:text-violet-300'],
            ['text' => 'text-rose-700 dark:text-rose-400', 'hover' => 'hover:text-rose-600 dark:hover:text-rose-300'],
            ['text' => 'text-amber-700 dark:text-amber-400', 'hover' => 'hover:text-amber-600 dark:hover:text-amber-300'],
            ['text' => 'text-lime-700 dark:text-lime-400', 'hover' => 'hover:text-lime-600 dark:hover:text-lime-300'],
        ];
        return $colors[array_rand($colors)];
    }
    
    public static function getTopCategories($limit = 10)
    {
        return Category::withCount('news')
            ->orderByDesc('news_count')
            ->take($limit)
            ->get();
    }

    /**
     * Check if a category exists by name or slug
     *
     * @param string $identifier Category name or slug
     * @return bool
     */
    public static function categoryExists($identifier)
    {
        return Category::where('status', 'active')
            ->where(function($query) use ($identifier) {
                $query->where('name', 'LIKE', '%' . $identifier . '%')
                      ->orWhere('slug', strtolower($identifier))
                      ->orWhere('slug', \Illuminate\Support\Str::slug($identifier));
            })
            ->exists();
    }

    /**
     * Get category by name or slug if it exists
     *
     * @param string $identifier Category name or slug
     * @return Category|null
     */
    public static function getCategory($identifier)
    {
        return Category::where('status', 'active')
            ->where(function($query) use ($identifier) {
                $query->where('name', 'LIKE', '%' . $identifier . '%')
                      ->orWhere('slug', strtolower($identifier))
                      ->orWhere('slug', \Illuminate\Support\Str::slug($identifier));
            })
            ->first();
    }

    /**
     * Get navigation menu items with existing categories only
     *
     * @return array Array of menu items with their existence status
     */
    public static function getNavigationMenuItems()
    {
        $menuCategories = [
            'international' => 'International',
            'sport' => 'Sport',
            'entertainment' => 'Entertainment',
            'business' => 'Business',
            'health' => 'Health',
            'science' => 'Science'
        ];

        $menuItems = [
            'home' => [
                'name' => 'Home',
                'url' => url('/'),
                'exists' => true, // Home always exists
                'icon' => 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
                'type' => 'link'
            ]
        ];

        $icons = [
            'international' => 'M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
            'sport' => 'M13 10V3L4 14h7v7l9-11h-7z',
            'entertainment' => 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 12l2 2 4-4',
            'business' => 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
            'health' => 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z',
            'science' => 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z'
        ];

        foreach ($menuCategories as $key => $name) {
            $category = self::getCategory($key);
            $menuItems[$key] = [
                'name' => $name,
                'url' => $category ? url('/category/' . $category->slug) : '#' . $key,
                'exists' => $category !== null,
                'icon' => $icons[$key],
                'category' => $category,
                'type' => 'link'
            ];
        }

        // Add Pages dropdown menu
        $menuItems['pages'] = [
            'name' => 'Pages',
            'url' => '#',
            'exists' => true,
            'icon' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
            'type' => 'dropdown',
            'dropdown_items' => self::getPagesDropdownItems()
        ];

        return $menuItems;
    }

    /**
     * Get pages dropdown menu items
     *
     * @return array Array of page menu items
     */
    public static function getPagesDropdownItems()
    {
        $pages = [
            [
                'name' => 'About Us',
                'route' => 'about'
            ],
            [
                'name' => 'Contact',
                'route' => 'contact'
            ],
            [
                'name' => 'Privacy Policy',
                'route' => 'privacy-policy'
            ],
            [
                'name' => 'Terms & Conditions',
                'route' => 'terms-and-conditions'
            ]
        ];

        // Filter pages to only include those with existing routes
        $validPages = [];
        foreach ($pages as $page) {
            if (self::routeExists($page['route'])) {
                $page['url'] = route($page['route']);
                $validPages[] = $page;
            }
        }

        return $validPages;
    }

    /**
     * Check if a route exists
     *
     * @param string $routeName
     * @return bool
     */
    public static function routeExists($routeName)
    {
        try {
            route($routeName);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
