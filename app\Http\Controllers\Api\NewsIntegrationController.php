<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LatestNewsTitle;
use App\Models\News;
use App\Services\NewsApiService;
use App\Services\NewsGeneratorService;
use Illuminate\Support\Facades\Log;

class NewsIntegrationController extends Controller
{
    protected $newsApiService;
    protected $newsGeneratorService;

    public function __construct(NewsApiService $newsApiService, NewsGeneratorService $newsGeneratorService)
    {
        $this->newsApiService = $newsApiService;
        $this->newsGeneratorService = $newsGeneratorService;
    }

    /**
     * Fetch latest news from external API
     *
     * @param \Illuminate\Http\Request $request
     * @param int $limit Number of articles to fetch
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchLatest(\Illuminate\Http\Request $request, $limit = 10)
    {
        try {
            // Get parameters from both path and query string
            $limit = (int) ($request->query('limit') ?: $limit);
            $country = $request->query('country');
            $language = $request->query('language');
            $category = $request->query('category');

            // Log the fetch request with parameters
            Log::info('News fetch request initiated', [
                'limit' => $limit,
                'country' => $country,
                'language' => $language,
                'category' => $category,
                'source' => 'api'
            ]);

            $stats = [
                'fetched' => 0,
                'new' => 0,
                'duplicates' => 0,
                'errors' => 0,
                'total' => LatestNewsTitle::count(),
                'processed' => LatestNewsTitle::where('is_processed', true)->count(),
                'generated' => LatestNewsTitle::where('is_generated', true)->count(),
                'pending' => LatestNewsTitle::where('is_processed', true)->where('is_generated', false)->count()
            ];

            // Pass all parameters to the service
            $params = [
                'limit' => $limit
            ];

            // Only add non-empty parameters
            if ($country) $params['country'] = $country;
            if ($language) $params['language'] = $language;
            if ($category) $params['category'] = $category;

            $articles = $this->newsApiService->fetchLatestNews($params);
            $stats['fetched'] = count($articles);

            if (empty($articles)) {
                return response()->json([
                    'success' => true,
                    'message' => 'No articles fetched from API',
                    'stats' => $stats,
                    'timestamp' => now()->toDateTimeString()
                ]);
            }

            // Process each article
            $processed = 0;
            foreach ($articles as $article) {

                if ($processed >= $limit) {
                    break;
                }

                try {
                    // Check if article already exists
                    if (LatestNewsTitle::exists($article['title'])) {
                        $stats['duplicates']++;
                        continue;
                    }
                    // Create new record
                    LatestNewsTitle::create([
                        'title' => $article['title'],
                        'country' => $country ? $country : null,
                        'language' => $language ? $language : null,
                        'source_published_at' => $article['source_published_at'] ?? now(),
                        'api_response' => json_encode($article),
                        'is_processed' => true,
                    ]);
                    
                    $stats['new']++;

                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error('Error processing article', [
                        'article' => $article,
                        'error' => $e->getMessage()
                    ]);
                }

                $processed++;
            }

            // Update stats after processing
            $stats['total'] = LatestNewsTitle::count();
            $stats['processed'] = LatestNewsTitle::where('is_processed', true)->count();
            $stats['generated'] = LatestNewsTitle::where('is_generated', true)->count();
            $stats['pending'] = LatestNewsTitle::where('is_processed', true)->where('is_generated', false)->count();

            // Log completion
            Log::info('News fetch completed successfully', [
                'stats' => $stats,
                'source' => 'api'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'News fetch completed successfully',
                'stats' => $stats,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            Log::error('News fetch failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'source' => 'api'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch news: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * Generate content using Gemini AI
     */
    public function generateContent($limit = 10)
    {
        try {
            $limit = (int) $limit;

            // Generate news articles using the service
            $results = $this->newsGeneratorService->generateNewsFromLatestTitles($limit);

            return response()->json([
                'success' => true,
                'message' => 'Content generation completed successfully',
                'results' => $results,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate content: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * Get system status and statistics
     */
    public function status()
    {
        try {
            $stats = [
                'total_titles' => LatestNewsTitle::count(),
                'processed' => LatestNewsTitle::where('is_processed', true)->count(),
                'generated' => LatestNewsTitle::where('is_generated', true)->count(),
                'pending_generation' => LatestNewsTitle::readyForGeneration()->count(),
                'recent_fetched_24h' => LatestNewsTitle::where('created_at', '>=', now()->subHours(24))->count(),
                'recent_generated_24h' => LatestNewsTitle::where('is_generated', true)
                    ->where('updated_at', '>=', now()->subHours(24))->count(),
                'total_news_articles' => News::count(),
                'timestamp' => now()->toDateTimeString()
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get status: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }
}
