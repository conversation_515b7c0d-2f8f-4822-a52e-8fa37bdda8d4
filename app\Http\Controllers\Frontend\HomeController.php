<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get only the latest featured news
        $featuredNews = News::published()
            ->latest('published_at')
            ->limit(1)
            ->get();

        // Get top stories by view count
        $topStories = News::published()
            ->orderBy('view_count', 'desc')
            ->limit(4)
            ->get();

        // Prepare excluded IDs (featured + top stories)
        $excludedIds = $featuredNews->pluck('id')->merge($topStories->pluck('id'));

        // Base query for recent news (excluding featured and top stories)
        $recentNewsQuery = News::published()
            ->whereNotIn('id', $excludedIds);

        // Get limited recent news
        $recentNews = (clone $recentNewsQuery)
            ->orderBy('published_at', 'desc')
            ->limit(20)
            ->get();

        // Determine if there are more recent news items
        $hasMoreRecentNews = $recentNewsQuery->count() > 20;

        // Get initial recent news (excluding featured news and top stories)
        $indiaNews = News::published()
            ->whereNotIn('id', $featuredNews->pluck('id')->merge($topStories->pluck('id')))
            ->whereHas('tags', function ($query) {
                $query->where('name', 'ahmedabad');
            })
            ->orderBy('published_at', 'desc')
            ->limit(4)
            ->get();

        // Get all active categories - using status column instead of active
        $categories = Category::where('status', 'active')
            ->orderBy('name')
            ->get();

        // Get top 15 popular tags by news count
        $popularTags = Tag::active()
            ->withCount('news')
            ->orderByDesc('news_count')
            ->orderBy('name')
            ->limit(15)
            ->get();

        $meta_title = config('app.name') . ' | Latest News Today | Breaking Headlines & Top Stories';
        $meta_description = config('app.name') . ' brings you the latest breaking news, trending headlines, and real-time updates on politics, entertainment, sports, tech, and more – all in one place.';
        $meta_keywords = config('app.name') . ' top headlines, breaking news, latest news, today news, live news updates, current affairs, top news headlines, trending news today, news live, world news, India news, breaking news today in India, latest updates on politics, sports news today, technology news updates, entertainment news headlines, live weather updates, financial news today, crime news updates, regional news in Hindi, top 10 news of the day, ' . config('app.name') . ' news, ' . config('app.name') . ' breaking updates';
        $meta_image = asset('storage/images/logo-color.svg');
        $canonical = url('/');
        $og_type = 'website';

        return view('frontend.home', compact(
            'featuredNews',
            'topStories',
            'recentNews',
            'categories',
            'indiaNews',
            'hasMoreRecentNews',
            'popularTags',
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_type'
        ));
    }

    public function loadMoreNews(Request $request)
    {
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 10);

        // Get featured and top stories IDs to exclude
        $featuredIds = News::published()->featured()->pluck('id');
        $topStoriesIds = News::published()->orderBy('view_count', 'desc')->limit(5)->pluck('id');
        $excludeIds = $featuredIds->merge($topStoriesIds);

        // Get more recent news
        $moreNews = News::published()
            ->whereNotIn('id', $excludeIds)
            ->orderBy('published_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();

        if ($moreNews->isEmpty()) {
            return response()->json(['html' => '', 'hasMore' => false]);
        }

        $html = view('frontend.news.news-items', ['moreNews' => $moreNews])->render();

        return response()->json([
            'html' => $html,
            'hasMore' => $moreNews->count() >= $limit
        ]);
    }
    public function privacyPolicy(Request $request)
    {
        $meta_title = 'Privacy Policy | ' . config('app.name');
        $meta_description = 'Understand how ' . config('app.name') . ' collects, uses, and protects your personal information. Your privacy is important to us.';
        $meta_keywords = 'privacy policy, data protection, user data, personal information, cookies, ' . config('app.name');
        $meta_image = asset('default-og-image.jpg');
        $canonical = url('/privacy-policy');
        $og_title = $meta_title;
        $og_description = $meta_description;
        $og_type = 'website';
        $robots_meta = 'index,follow';
        $published_at = now()->format('Y-m-d\TH:i:sP');
        $updated_at = now()->format('Y-m-d\TH:i:sP');

        return view('frontend.pages.privacy-policy', compact(
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'robots_meta',
            'published_at',
            'updated_at'
        ));
    }
    public function termsAndConditions(Request $request)
    {
        $meta_title = 'Terms and Conditions | ' . config('app.name');
        $meta_description = 'Read the Terms and Conditions of using ' . config('app.name') . '. By accessing or using our services, you agree to our terms, policies, and user responsibilities.';
        $meta_keywords = 'terms and conditions, user agreement, website policy, legal terms, ' . config('app.name');
        $canonical = url('/terms-and-conditions');
        $og_title = $meta_title;
        $og_description = $meta_description;
        $og_type = 'website';
        $meta_image = asset('default-og-image.jpg');
        $published_at = now()->format('Y-m-d\TH:i:sP');
        $updated_at = now()->format('Y-m-d\TH:i:sP');
        $robots_meta = 'index,follow';

        return view('frontend.pages.terms-and-conditions', compact(
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'robots_meta',
            'published_at',
            'updated_at'
        ));
    }
    public function about(Request $request)
    {
        $meta_title = 'About Us | ' . config('app.name');
        $meta_description = 'Learn more about ' . config('app.name') . ', our mission, values, and the team behind our success. Discover what drives us to deliver quality and trust.';
        $meta_keywords = 'about us, company profile, mission, vision, team, ' . config('app.name');
        $canonical = url('/about-us');
        $og_title = $meta_title;
        $og_description = $meta_description;
        $og_type = 'website';
        $meta_image = asset('default-og-image.jpg');
        $published_at = now()->format('Y-m-d\TH:i:sP');
        $updated_at = now()->format('Y-m-d\TH:i:sP');
        $robots_meta = 'index,follow';

        return view('frontend.pages.about', compact(
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'robots_meta',
            'published_at',
            'updated_at'
        ));
    }
}
