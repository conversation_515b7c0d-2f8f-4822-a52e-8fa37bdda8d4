<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class Api<PERSON>ey extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'provider',
        'owner',
        'key',
        'status',
        'today_count',
        'total_count',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'today_count' => 'integer',
        'total_count' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'key',
    ];

    /**
     * Encrypt the API key when storing
     */
    public function setKeyAttribute($value)
    {
        $this->attributes['key'] = Crypt::encryptString($value);
    }

    /**
     * Decrypt the API key when retrieving
     */
    public function getKeyAttribute($value)
    {
        try {
            return Crypt::decryptString($value);
        } catch (\Exception $e) {
            return $value; // Return as-is if decryption fails
        }
    }

    /**
     * Get masked API key for display
     */
    public function getMaskedKeyAttribute()
    {
        $key = $this->key;
        if (strlen($key) <= 8) {
            return str_repeat('*', strlen($key));
        }
        return substr($key, 0, 4) . str_repeat('*', strlen($key) - 8) . substr($key, -4);
    }

    /**
     * Increment usage counters
     */
    public function incrementUsage()
    {
        $this->increment('today_count');
        $this->increment('total_count');
    }

    /**
     * Reset today's count (typically called daily via cron)
     */
    public function resetTodayCount()
    {
        $this->update(['today_count' => 0]);
    }

    /**
     * Set this key as the active one and deactivate others for the same provider
     */
    public function setAsActive()
    {
        // Deactivate all other keys for the same provider
        static::where('provider', $this->provider)
            ->where('id', '!=', $this->id)
            ->update(['is_active' => false]);

        // Activate this key
        $this->update(['is_active' => true]);
    }

    /**
     * Get the currently active key for a specific provider
     */
    public static function getActiveKey($provider)
    {
        return static::where('provider', $provider)
            ->where('status', 'active')
            ->where('is_active', true)
            ->first();
    }

    /**
     * Scope to filter by status
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to filter by provider
     */
    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope to filter by owner
     */
    public function scopeByOwner($query, $owner)
    {
        return $query->where('owner', $owner);
    }

    /**
     * Get available API providers
     */
    public static function getAvailableProviders()
    {
        return [
            'gemini' => 'Google Gemini',
            'openai' => 'OpenAI',
            'anthropic' => 'Anthropic Claude',
            'cohere' => 'Cohere',
            'huggingface' => 'Hugging Face',
        ];
    }
}
