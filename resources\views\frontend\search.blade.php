@extends('layouts.frontend')

@section('title', 'Search Results: ' . $query)

@section('content')
<section class="bg-gray-50 dark:bg-gray-900 py-8 container mx-auto transition-colors duration-200">
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- Search Header -->
        <h1 class="relative border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white mb-6 before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] transition-colors duration-200">
            Search Results: {{ $query }}
        </h1>

        <!-- Search Results -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8" id="search-results-container">
            @forelse($articles as $news)
            <article class="py-0 flex">
                <a class="article-image w-2/5 flex-shrink-0" href="{{ route('news.show', $news->slug) }}">
                    <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
                        <img alt="{{ $news->title }}"
                            loading="lazy"
                            decoding="async"
                            class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                            style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                            src="{{ asset('storage/' . $news->main_image) }}"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <!-- Image placeholder -->
                        <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                            <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                    </div>
                </a>
                <div class="ml-3 flex-1">
                    @if($news->categories->isNotEmpty())
                    <a class="text-xs font-medium uppercase tracking-widest transition-colors duration-300 hover:opacity-90"
                        href="{{ route('category.show', $news->categories->first()->slug) }}"
                        style="color: <?= $news->categories->first()->color ?>;">
                        {{ $news->categories->first()->name }}
                    </a>
                    @endif
                    <a href="{{ route('news.show', $news->slug) }}">
                        <h3 class="text-sm lg:text-sm font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 dark:decoration-gray-200 decoration-2 transition duration-300 ease-in-out">
                            {{ $news->title }}
                        </h3>
                    </a>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {{ $news->published_at->format('M d, Y') }}
                    </p>
                </div>
            </article>
            @empty
            <div class="col-span-2 text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No results found</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">We couldn't find any articles matching your search.</p>
            </div>
            @endforelse
        </div>

        <div id="loading-indicator" class="text-center py-6 hidden">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-600"></div>
        </div>

        @if($hasMore)
        <div id="load-more-container" class="mt-8 flex justify-center">
            <button id="load-more-btn" class="text-black dark:text-white py-3 px-10 my-2 mx-1 rounded-md text-sm font-medium shadow-sm hover:shadow-md hover:scale-105 hover:opacity-90 transition-all duration-300 ease-in-out whitespace-nowrap inline-block hover:bg-gradient-to-r hover:from-blue-100 hover:to-transparent cursor-pointer">
                Load More
            </button>
        </div>
        @endif

        <div id="end-of-content" class="text-center py-6 hidden">
            <p class="text-gray-500 dark:text-gray-400">No more results to load</p>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let offset = <?= count($articles) ?>;
        const limit = 10;
        let loading = false;
        let hasMore = true;

        const loadingIndicator = document.getElementById('loading-indicator');
        const loadMoreBtn = document.getElementById('load-more-btn');
        const loadMoreContainer = document.getElementById('load-more-container');
        const endOfContent = document.getElementById('end-of-content');
        const searchResultsContainer = document.getElementById('search-results-container');

        function loadMoreResults() {
            if (loading || !hasMore) return;

            loading = true;
            loadingIndicator.classList.remove('hidden');
            loadMoreBtn.disabled = true;

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Fetch more results
            fetch('{{ route("search.load-more") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        query: "{{ $query }}",
                        offset: offset,
                        limit: limit
                    })
                })
                .then(response => response.json())
                .then(data => {
                    loadingIndicator.classList.add('hidden');
                    loadMoreBtn.disabled = false;

                    if (data.html) {
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = data.html;

                        // Append each child individually to maintain event listeners
                        while (tempDiv.firstChild) {
                            searchResultsContainer.appendChild(tempDiv.firstChild);
                        }

                        // Update offset
                        offset += limit;
                    }

                    hasMore = data.hasMore;
                    if (!hasMore) {
                        loadMoreContainer.classList.add('hidden');
                        endOfContent.classList.remove('hidden');
                    }

                    loading = false;
                })
                .catch(error => {
                    console.error('Error loading more results:', error);
                    loadingIndicator.classList.add('hidden');
                    loadMoreBtn.disabled = false;
                    loading = false;
                });
        }

        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', loadMoreResults);
        }
    });
</script>
@endpush