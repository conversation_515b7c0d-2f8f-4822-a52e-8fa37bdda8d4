<?php

namespace Database\Seeders;

use App\Models\ApiKey;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ApiKeySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample API keys for different providers
        $apiKeys = [
            [
                'provider' => 'gemini',
                'owner' => '<PERSON>',
                'key' => 'AIzaSyDemoKey1234567890abcdefghijklmnopqrstuvwxyz',
                'status' => 'active',
                'today_count' => 15,
                'total_count' => 245,
                'is_active' => true,
            ],
            [
                'provider' => 'openai',
                'owner' => '<PERSON>',
                'key' => 'sk-demo1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                'status' => 'active',
                'today_count' => 8,
                'total_count' => 156,
                'is_active' => false,
            ],
            [
                'provider' => 'anthropic',
                'owner' => '<PERSON>',
                'key' => 'sk-ant-demo1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                'status' => 'active',
                'today_count' => 3,
                'total_count' => 89,
                'is_active' => false,
            ],
            [
                'provider' => 'cohere',
                'owner' => 'Sarah Wilson',
                'key' => 'co-demo1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                'status' => 'inactive',
                'today_count' => 0,
                'total_count' => 23,
                'is_active' => false,
            ],
            [
                'provider' => 'huggingface',
                'owner' => 'David Brown',
                'key' => 'hf_demo1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                'status' => 'active',
                'today_count' => 12,
                'total_count' => 178,
                'is_active' => false,
            ],
            [
                'provider' => 'gemini',
                'owner' => 'Admin User',
                'key' => 'AIzaSyBackupKey1234567890abcdefghijklmnopqrstuvwxyz',
                'status' => 'active',
                'today_count' => 0,
                'total_count' => 67,
                'is_active' => false,
            ],
        ];

        foreach ($apiKeys as $apiKeyData) {
            ApiKey::create($apiKeyData);
        }
    }
}
