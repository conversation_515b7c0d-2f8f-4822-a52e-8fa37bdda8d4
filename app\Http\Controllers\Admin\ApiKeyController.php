<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class ApiKeyController extends Controller
{
    /**
     * Display a listing of API keys and handle datatable requests.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            $query = ApiKey::query();
            
            return DataTables::of($query)
                ->addColumn('api_info', function ($apiKey) {
                    $providers = ApiKey::getAvailableProviders();
                    $providerName = $providers[$apiKey->provider] ?? ucfirst($apiKey->provider);

                    return '<div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="material-icons text-blue-600 text-sm">vpn_key</i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">' . e($providerName) . '</div>
                            <div class="text-sm text-gray-500">' . e($apiKey->masked_key) . '</div>
                        </div>
                    </div>';
                })
                ->addColumn('owner_info', function ($apiKey) {
                    return '<div class="text-sm text-gray-900">' . e($apiKey->owner) . '</div>';
                })
                ->addColumn('status', function ($apiKey) {
                    $isActive = $apiKey->status === 'active';
                    return DataTableHelper::statusBadge(
                        $isActive, 
                        'Active', 
                        'Inactive', 
                        'verified', 
                        'unverified'
                    );
                })
                ->addColumn('usage', function ($apiKey) {
                    return '<div class="text-center">
                        <div class="text-sm font-medium text-gray-900">' . number_format($apiKey->today_count) . '</div>
                        <div class="text-xs text-gray-500">Today</div>
                    </div>';
                })
                ->addColumn('total_usage', function ($apiKey) {
                    return '<div class="text-center">
                        <div class="text-sm font-medium text-gray-900">' . number_format($apiKey->total_count) . '</div>
                        <div class="text-xs text-gray-500">Total</div>
                    </div>';
                })
                ->addColumn('is_active_badge', function ($apiKey) {
                    if ($apiKey->is_active) {
                        return '<span class="status-badge verified">Currently Used</span>';
                    }
                    return '<span class="status-badge unverified">Not Used</span>';
                })
                ->addColumn('actions', function ($apiKey) {
                    return DataTableHelper::actionsColumn($apiKey, 'admin.api-keys', [
                        'viewTitle' => 'View API Key',
                        'editTitle' => 'Edit API Key',
                        'deleteTitle' => 'Delete API Key'
                    ]);
                })
                ->editColumn('created_at', function ($apiKey) {
                    return $apiKey->created_at->format('M d, Y');
                })
                ->rawColumns(['api_info', 'owner_info', 'status', 'usage', 'total_usage', 'is_active_badge', 'actions'])
                ->make(true);
        }
        
        return view('admin.api-keys.index');
    }

    /**
     * Show the form for creating a new API key.
     */
    public function create()
    {
        $providers = ApiKey::getAvailableProviders();
        return view('admin.api-keys.create', compact('providers'));
    }

    /**
     * Store a newly created API key in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'provider' => ['required', 'string', 'max:255'],
            'owner' => ['required', 'string', 'max:255'],
            'key' => ['required', 'string', 'max:1000'],
            'status' => ['required', 'in:active,inactive'],
            'is_active' => ['boolean'],
        ]);

        $apiKey = ApiKey::create([
            'provider' => $request->provider,
            'owner' => $request->owner,
            'key' => $request->key,
            'status' => $request->status,
            'is_active' => $request->boolean('is_active', false),
            'today_count' => 0,
            'total_count' => 0,
        ]);

        // If this key is set as active, deactivate others for the same provider
        if ($apiKey->is_active) {
            $apiKey->setAsActive();
        }

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'API key created successfully.');
    }

    /**
     * Display the specified API key.
     */
    public function show(ApiKey $apiKey)
    {
        $providers = ApiKey::getAvailableProviders();
        return view('admin.api-keys.show', compact('apiKey', 'providers'));
    }

    /**
     * Show the form for editing the specified API key.
     */
    public function edit(ApiKey $apiKey)
    {
        $providers = ApiKey::getAvailableProviders();
        return view('admin.api-keys.edit', compact('apiKey', 'providers'));
    }

    /**
     * Update the specified API key in storage.
     */
    public function update(Request $request, ApiKey $apiKey)
    {
        $request->validate([
            'provider' => ['required', 'string', 'max:255'],
            'owner' => ['required', 'string', 'max:255'],
            'key' => ['required', 'string', 'max:1000'],
            'status' => ['required', 'in:active,inactive'],
            'is_active' => ['boolean'],
        ]);

        $apiKey->update([
            'provider' => $request->provider,
            'owner' => $request->owner,
            'key' => $request->key,
            'status' => $request->status,
            'is_active' => $request->boolean('is_active', false),
        ]);

        // If this key is set as active, deactivate others for the same provider
        if ($apiKey->is_active) {
            $apiKey->setAsActive();
        }

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'API key updated successfully.');
    }

    /**
     * Remove the specified API key from storage.
     */
    public function destroy(ApiKey $apiKey)
    {
        $apiKey->delete();

        return response()->json([
            'success' => true,
            'message' => 'API key deleted successfully.'
        ]);
    }

    /**
     * Set API key as active for its provider
     */
    public function setActive(ApiKey $apiKey)
    {
        $apiKey->setAsActive();

        return response()->json([
            'success' => true,
            'message' => 'API key set as active successfully.'
        ]);
    }
}
