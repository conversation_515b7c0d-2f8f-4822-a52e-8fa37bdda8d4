<!-- Mobile sidebar overlay -->
<div id="sidebar-overlay" class="fixed inset-0 flex z-40 md:hidden hidden opacity-0 transition-opacity duration-300">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
</div>

<!-- Sidebar -->
<div id="admin-sidebar" class="fixed inset-y-0 left-0 flex flex-col w-56 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 -translate-x-full md:translate-x-0 md:static md:inset-0">

    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-14 px-4 bg-blue-600">
        <div class="flex items-center space-x-2">
            <div class="w-7 h-7 bg-white rounded-lg flex items-center justify-center">
                <span class="text-blue-600 font-bold text-sm">A</span>
            </div>
            <span class="text-lg font-bold text-white">Admin</span>
        </div>
        <button id="sidebar-close-btn" class="md:hidden text-white hover:text-gray-200">
            <i class="material-icons text-sm">close</i>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-3 py-4 overflow-y-auto">
        <div class="space-y-1">
            <!-- Remove the PHP debug code -->
            <!-- Settings Menu -->
            <div class="">
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                    class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                    <i class="material-icons mr-3 text-lg">dashboard</i>
                    Dashboard
                </a>
            </div>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users_access')): ?>
            <div data-dropdown="<?php echo e(request()->routeIs('admin.users*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                    class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.users*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">people</i>
                        <span class="font-medium text-sm">Users</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5">
                    <a href="<?php echo e(route('admin.users.index')); ?>"
                        class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.users.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All Users
                    </a>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users_create')): ?>
                    <a href="<?php echo e(route('admin.users.create')); ?>"
                        class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.users.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">add</i>
                        Add User
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Roles Management -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role_access')): ?>
            <div data-dropdown="<?php echo e(request()->routeIs('admin.roles*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                    class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.roles*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">verified_user</i>
                        <span class="font-medium text-sm">Roles</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles_view')): ?>
                    <a href="<?php echo e(route('admin.roles.index')); ?>"
                        class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.roles.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All Roles
                    </a>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles_create')): ?>
                    <a href="<?php echo e(route('admin.roles.create')); ?>"
                        class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.roles.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">add_moderator</i>
                        Add Role
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Permissions Management -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions_access')): ?>
            <div data-dropdown="<?php echo e(request()->routeIs('admin.permissions*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                    class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.permissions*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">key</i>
                        <span class="font-medium text-sm">Permissions</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions_view')): ?>
                    <a href="<?php echo e(route('admin.permissions.index')); ?>"
                        class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.permissions.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All Permissions
                    </a>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions_create')): ?>
                    <a href="<?php echo e(route('admin.permissions.create')); ?>"
                        class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.permissions.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">vpn_key</i>
                        Add Permission
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Categories Menu -->
            <div data-dropdown="<?php echo e(request()->routeIs('admin.categories*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                        class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.categories*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">category</i>
                        <span class="font-medium text-sm">Categories</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5 <?php echo e(request()->routeIs('admin.categories*') ? 'block' : 'hidden'); ?>">
                    <a href="<?php echo e(route('admin.categories.index')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.categories.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All Categories
                    </a>
                    <a href="<?php echo e(route('admin.categories.create')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.categories.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">add</i>
                        Add Category
                    </a>
                </div>
            </div>

            <!-- Tags Menu -->
            <div data-dropdown="<?php echo e(request()->routeIs('admin.tags*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                        class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.tags*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">local_offer</i>
                        <span class="font-medium text-sm">Tags</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5 <?php echo e(request()->routeIs('admin.tags*') ? 'block' : 'hidden'); ?>">
                    <a href="<?php echo e(route('admin.tags.index')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.tags.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All Tags
                    </a>
                    <a href="<?php echo e(route('admin.tags.create')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.tags.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">add</i>
                        Add Tag
                    </a>
                </div>
            </div>

            <!-- News Menu -->
            <div data-dropdown="<?php echo e(request()->routeIs('admin.news*') || request()->routeIs('admin.news-integration*') || request()->routeIs('admin.pending-news*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                        class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.news*') || request()->routeIs('admin.news-integration*') || request()->routeIs('admin.pending-news*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">article</i>
                        <span class="font-medium text-sm">News</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5 <?php echo e(request()->routeIs('admin.news*') || request()->routeIs('admin.news-integration*') || request()->routeIs('admin.pending-news*') ? 'block' : 'hidden'); ?>">
                    <a href="<?php echo e(route('admin.news.index')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.news.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All News
                    </a>
                    <a href="<?php echo e(route('admin.news.create')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.news.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">add</i>
                        Add News
                    </a>
                    <a href="<?php echo e(route('admin.pending-news.index')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.pending-news*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">pending</i>
                        Pending Approval
                        <span id="pending-count-badge" class="ml-auto bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-0.5 rounded-full hidden">0</span>
                    </a>
                    <a href="<?php echo e(route('admin.news-integration.index')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.news-integration*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">auto_awesome</i>
                        AI Integration
                    </a>
                </div>
            </div>

            <!-- Login Logs & Security -->
            <div class="mb-2">
                <a href="<?php echo e(route('admin.login-logs.index')); ?>"
                    class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.login-logs.*') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                    <i class="material-icons mr-3 text-lg">security</i>
                    Login Logs
                </a>
            </div>

            <!-- Contact Messages -->
            <div class="mb-2">
                <a href="<?php echo e(route('admin.contacts.index')); ?>"
                    class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.contacts.*') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                    <i class="material-icons mr-3 text-lg">contact_mail</i>
                    Contact Messages
                </a>
            </div>

            <!-- API Keys Menu -->
            <div data-dropdown="<?php echo e(request()->routeIs('admin.api-keys*') ? 'true' : 'false'); ?>">
                <button data-dropdown-button
                        class="flex items-center justify-between w-full px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 <?php echo e(request()->routeIs('admin.api-keys*') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                    <div class="flex items-center">
                        <i class="material-icons mr-2 text-lg">vpn_key</i>
                        <span class="font-medium text-sm">API Keys</span>
                    </div>
                    <i class="material-icons transform transition-transform duration-200 text-lg" data-dropdown-icon>
                        keyboard_arrow_down
                    </i>
                </button>
                <div data-dropdown-menu class="ml-5 mt-1 space-y-0.5 <?php echo e(request()->routeIs('admin.api-keys*') ? 'block' : 'hidden'); ?>">
                    <a href="<?php echo e(route('admin.api-keys.index')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.api-keys.index') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">list</i>
                        All API Keys
                    </a>
                    <a href="<?php echo e(route('admin.api-keys.create')); ?>"
                       class="flex items-center px-3 py-1.5 text-xs text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 <?php echo e(request()->routeIs('admin.api-keys.create') ? 'bg-blue-50 text-blue-600' : ''); ?>">
                        <i class="material-icons mr-2 text-sm">add</i>
                        Add API Key
                    </a>
                </div>
            </div>

            <!-- Settings Menu -->
            <div class="mb-2">
                <a href="<?php echo e(route('admin.settings.index')); ?>"
                    class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.settings.*') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                    <i class="material-icons mr-3 text-lg">settings</i>
                    Settings
                </a>
            </div>

            <!-- Logs Menu -->
            <div>
                <a href="<?php echo e(route('admin.logs.index')); ?>"
                   class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.logs.index') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                    <i class="material-icons mr-3 text-lg">bug_report</i>
                    Logs
                </a>
            </div>
        </div>
    </nav>
    <!-- User Profile Section -->
    <div class="p-3 border-t border-gray-200">
        <a href="<?php echo e(route('admin.profile.show')); ?>" class="flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200">
            <?php if(auth()->check() && auth()->user()->profile_image): ?>
            <img src="<?php echo e(asset('storage/' . auth()->user()->profile_image)); ?>"
                alt="<?php echo e(auth()->user()->name); ?>"
                class="w-8 h-8 rounded-full object-cover">
            <?php else: ?>
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span class="text-white font-medium text-xs">
                    <?php echo e(auth()->check() ? substr(auth()->user()->name, 0, 1) : 'A'); ?>

                </span>
            </div>
            <?php endif; ?>
            <div class="flex-1 min-w-0">
                <p class="text-xs font-medium text-gray-900 truncate">
                    <?php echo e(auth()->check() ? auth()->user()->name : 'Admin User'); ?>

                </p>
                <p class="text-xs text-gray-500 truncate">
                    <?php echo e(auth()->check() ? auth()->user()->email : '<EMAIL>'); ?>

                </p>
            </div>
            <i class="material-icons text-gray-400 text-sm">chevron_right</i>
        </a>
    </div>
</div><?php /**PATH C:\projects\client-admin-panel\resources\views/components/admin/sidebar.blade.php ENDPATH**/ ?>