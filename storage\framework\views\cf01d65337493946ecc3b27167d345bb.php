<?php $__env->startSection('title', 'Edit API Key'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8">
    <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Edit API Key</h1>
        <p class="text-gray-600">Update API key information for <?php echo e($providers[$apiKey->author] ?? ucfirst($apiKey->author)); ?></p>
    </div>
    <div class="flex items-center space-x-3 mt-4 sm:mt-0">
        <a href="<?php echo e(route('admin.api-keys.show', $apiKey)); ?>" 
           class="material-button material-button-secondary">
            <i class="material-icons text-sm mr-2">visibility</i>
            View Details
        </a>
        <a href="<?php echo e(route('admin.api-keys.index')); ?>" 
           class="material-button material-button-secondary">
            <i class="material-icons text-sm mr-2">arrow_back</i>
            Back to API Keys
        </a>
    </div>
</div>

<!-- Edit Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">API Key Information</h3>
        <p class="mt-1 text-sm text-gray-600">Update the details for your API key</p>
    </div>

    <div class="p-6">
        <form id="api_key_form" action="<?php echo e(route('admin.api-keys.update', $apiKey)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>

        <!-- Form Layout - Responsive Grid -->
        <div class="space-y-6">
            <!-- Row 1: Provider and Status -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Provider Field -->
                <div class="order-1 lg:order-1">
                    <label for="provider" class="block text-sm font-medium text-gray-700 mb-2">
                        API Provider <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">business</i>
                        </div>
                        <select id="provider" name="provider" required
                            class="material-input-with-icon <?php $__errorArgs = ['provider'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Select API Provider</option>
                            <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('provider', $apiKey->provider) == $key ? 'selected' : ''); ?>>
                                    <?php echo e($name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <?php $__errorArgs = ['provider'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Status Field -->
                <div class="order-2 lg:order-2">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Status <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">toggle_on</i>
                        </div>
                        <select id="status" name="status" required
                            class="material-input-with-icon <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Select Status</option>
                            <option value="active" <?php echo e(old('status', $apiKey->status) == 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(old('status', $apiKey->status) == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                        </select>
                    </div>
                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Row 2: Owner -->
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <label for="owner" class="block text-sm font-medium text-gray-700 mb-2">
                        Key Owner <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">person</i>
                        </div>
                        <input id="owner" name="owner" type="text" required value="<?php echo e(old('owner', $apiKey->owner)); ?>"
                            class="material-input-with-icon <?php $__errorArgs = ['owner'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            placeholder="Enter key owner name">
                    </div>
                    <?php $__errorArgs = ['owner'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Row 3: API Key -->
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <label for="key" class="block text-sm font-medium text-gray-700 mb-2">
                        API Key <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">vpn_key</i>
                        </div>
                        <textarea id="key" name="key" rows="3" required
                            class="material-input-with-icon <?php $__errorArgs = ['key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            placeholder="Enter your API key"><?php echo e(old('key', $apiKey->key)); ?></textarea>
                    </div>
                    <?php $__errorArgs = ['key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <p class="mt-1 text-sm text-gray-500">
                        <i class="material-icons text-xs mr-1">security</i>
                        Your API key will be encrypted and stored securely
                    </p>
                </div>
            </div>

            <!-- Row 3: Usage Statistics (Read-only) -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Today's Usage</label>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2">
                        <span class="text-lg font-semibold text-gray-900"><?php echo e(number_format($apiKey->today_count)); ?></span>
                        <span class="text-sm text-gray-500 ml-2">requests today</span>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Total Usage</label>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2">
                        <span class="text-lg font-semibold text-gray-900"><?php echo e(number_format($apiKey->total_count)); ?></span>
                        <span class="text-sm text-gray-500 ml-2">total requests</span>
                    </div>
                </div>
            </div>

            <!-- Row 4: Currently Used -->
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <div class="flex items-center">
                        <input id="is_active" name="is_active" type="checkbox" value="1"
                            <?php echo e(old('is_active', $apiKey->is_active) ? 'checked' : ''); ?>

                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                            Set as currently used key for this provider
                        </label>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        If checked, this key will be used for API calls and other keys for the same provider will be deactivated
                    </p>
                    <?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 mt-8">
            <a href="<?php echo e(route('admin.api-keys.index')); ?>" 
               class="material-button material-button-secondary">
                Cancel
            </a>
            <button type="submit" class="material-button material-button-primary">
                <i class="material-icons text-sm mr-2">save</i>
                Update API Key
            </button>
        </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery Validation -->
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>

<script>
$(document).ready(function() {
    // jQuery Form validation
    $("#api_key_form").validate({
        rules: {
            author: {
                required: true
            },
            key: {
                required: true,
                minlength: 10,
                maxlength: 1000
            },
            status: {
                required: true
            }
        },
        messages: {
            author: {
                required: "Please select an API provider"
            },
            key: {
                required: "Please enter the API key",
                minlength: "API key must be at least 10 characters long",
                maxlength: "API key cannot exceed 1000 characters"
            },
            status: {
                required: "Please select a status"
            }
        },
        errorElement: 'p',
        errorClass: 'mt-1 text-sm text-red-600',
        highlight: function(element) {
            $(element).addClass('material-input-error');
        },
        unhighlight: function(element) {
            $(element).removeClass('material-input-error');
        },
        errorPlacement: function(error, element) {
            error.insertAfter(element.closest('.relative'));
        }
    });

    // Auto-resize textarea
    $('#key').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/api-keys/edit.blade.php ENDPATH**/ ?>