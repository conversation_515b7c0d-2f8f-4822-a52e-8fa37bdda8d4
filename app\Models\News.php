<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class News extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'main_image',
        'sub_image',
        'status',
        'approval_status',
        'approved_at',
        'approved_by',
        'rejection_reason',
        'published_at',
        'sort_order',
        'is_featured',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'robots_meta',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'approved_at' => 'datetime',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($news) {
            if (empty($news->slug)) {
                // $news->slug = Str::slug($news->title);
                $slug= Str::slug($news->title);
                // $tempLSug = $news->slug;
                if (empty($slug)) {
                    // Fallback: Replace spaces with hyphens, keep Unicode characters
                    $slug = preg_replace('/\s+/u', '-', trim($news->title));
                    $slug = mb_strtolower($slug, 'UTF-8'); // optional: lowercase
                }
                $news->slug = $slug;

            }
            if (empty($news->published_at) && $news->status === 'active') {
                $news->published_at = now();
            }
        });

        static::updating(function ($news) {
            if ($news->isDirty('title') && empty($news->slug)) {
                $slug= Str::slug($news->title);
                if (empty($slug)) {
                    // Fallback: Replace spaces with hyphens, keep Unicode characters
                    $slug = preg_replace('/\s+/u', '-', trim($news->title));
                    $slug = mb_strtolower($slug, 'UTF-8'); // optional: lowercase
                }
                $news->slug = $slug;
            }

            if ($news->isDirty('status') && $news->status === 'active' && empty($news->published_at)) {
                $news->published_at = now();
            }
        });
    }

    /**
     * Scope for active news
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for featured news
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for published news
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'active')
                    ->where('approval_status', 'approved')
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope for pending approval news
     */
    public function scopePendingApproval($query)
    {
        return $query->where('approval_status', 'pending');
    }

    /**
     * Scope for approved news
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope for rejected news
     */
    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    /**
     * The categories that belong to the news.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_news');
    }

    /**
     * The tags that belong to the news.
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'news_tag');
    }

    /**
     * The user who approved this news.
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the main image URL
     */
    public function getMainImageUrlAttribute()
    {
        if ($this->main_image) {
            return asset('storage/' . $this->main_image);
        }
        return "data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e";
    }

    /**
     * Get the sub image URL
     */
    public function getSubImageUrlAttribute()
    {
        if ($this->sub_image) {
            return asset('storage/' . $this->sub_image);
        }
        return null;
    }

    /**
     * Get the OG image URL
     */
    public function getOgImageUrlAttribute()
    {
        if ($this->og_image) {
            return asset('storage/' . $this->og_image);
        }
        return $this->main_image_url;
    }
}
