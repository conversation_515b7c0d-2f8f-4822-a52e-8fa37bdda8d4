<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Route;
use App\Models\News;
use App\Models\Category;
use App\Models\Tag;

class SitemapController extends Controller
{
    protected static int $chunkSize;

    public function __construct()
    {
        self::$chunkSize = (int) env('SITEMAP_CHUNK_SIZE', 1000);
    }
    // Sitemap index
    public function index()
    {
        $types = ['news', 'categories', 'tags','pages'];
        $sitemaps = [];
        // $sitemaps[] = [
        //     'loc' => url('/'),
        //     // 'changefreq' => 'always',
        //     // 'priority' => '1.0',
        //     'lastmod' => now()->toAtomString(), // Set lastmod to now or homepage updated time
        // ];
        // Generate sitemap chunk entries
        foreach ($types as $type) {
            if ($type === 'pages') {
                // Handle static pages
                $staticPages = $this->getStaticPages();
                $count = count($staticPages);
                $chunks = ceil($count / self::$chunkSize);

                for ($i = 1; $i <= $chunks; $i++) {
                    $sitemaps[] = [
                        'loc' => url("/sitemap_{$type}_{$i}.xml"),
                        'lastmod' => now()->toAtomString(),
                    ];
                }
            } else {
                $model = $this->getModel($type);
                $count = $model::count();
                $chunks = ceil($count / self::$chunkSize);

                for ($i = 1; $i <= $chunks; $i++) {
                    // Optionally get last updated item in chunk range
                    $lastItem = $model::select('updated_at')
                        ->skip(($i - 1) * self::$chunkSize)
                        ->take(self::$chunkSize)
                        ->orderByDesc('updated_at')
                        ->first();

                    $sitemaps[] = [
                        'loc' => url("/sitemap_{$type}_{$i}.xml"),
                        'lastmod' => optional($lastItem)->updated_at?->toAtomString() ?? now()->toAtomString(),
                    ];
                }
            }
        }
        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= view('sitemap.index', ['sitemaps' => $sitemaps])->render();

        return response($xml, 200)->header('Content-Type', 'application/xml');
    }

    // Individual sitemap chunks
    public function chunked($type, $chunk)
    {
        // Handle static pages differently
        if ($type === 'pages') {
            return $this->generateStaticPagesChunk($chunk);
        }

        $model = $this->getModel($type);

        $items = $model::select('slug', 'updated_at')
            ->skip(($chunk - 1) * self::$chunkSize)
            ->take(self::$chunkSize)
            ->get();

        $urls = $items->map(function ($item) use ($type) {
            $urlPrefix = match ($type) {
                'news' => 'news',
                'categories' => 'category',
                'tags' => 'tag',
            };

            $changefreq = match ($type) {
                'news' => 'hourly',
                'categories' => 'daily',
                'tags' => 'daily',
                default => 'daily',
            };
            $priority = match ($type) {
                'news' => '0.9',
                'categories' => '0.8',
                'tags' => '0.8',
                default => '1.0',
            };

            return [
                'loc' => url("/{$urlPrefix}/" . $item->slug),
                'lastmod' => $item->updated_at->toAtomString(),
                'changefreq' => $changefreq,
                'priority' => $priority,
            ];
        });

        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= view('sitemap.chunk', ['urls' => $urls])->render();

        return response($xml, 200)->header('Content-Type', 'application/xml');
    }

    private function getModel($type)
    {
        return match ($type) {
            'news' => News::class,
            'categories' => Category::class,
            'tags' => Tag::class,
            default => abort(404)
        };
    }

    /**
     * Get static pages for sitemap
     */
    private function getStaticPages()
    {
        $pages = [];

        // Home page (always exists)
        $pages[] = [
            'url' => url('/'),
            'changefreq' => 'daily',
            'priority' => '1.0',
            'lastmod' => now()->toAtomString(),
        ];

        // Static pages with route checking
        $staticRoutes = [
            'about' => ['changefreq' => 'monthly', 'priority' => '0.8'],
            'contact' => ['changefreq' => 'monthly', 'priority' => '0.8'],
            'privacy-policy' => ['changefreq' => 'yearly', 'priority' => '0.5'],
            'terms-and-conditions' => ['changefreq' => 'yearly', 'priority' => '0.5'],
        ];

        foreach ($staticRoutes as $routeName => $config) {
            if ($this->routeExists($routeName)) {
                $pages[] = [
                    'url' => route($routeName),
                    'changefreq' => $config['changefreq'],
                    'priority' => $config['priority'],
                    'lastmod' => now()->toAtomString(),
                ];
            }
        }

        return $pages;
    }

    /**
     * Check if a route exists
     */
    private function routeExists($routeName)
    {
        try {
            route($routeName);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Generate static pages chunk for sitemap
     */
    private function generateStaticPagesChunk($chunk)
    {
        $staticPages = $this->getStaticPages();

        // Get the chunk of static pages
        $startIndex = ($chunk - 1) * self::$chunkSize;
        $chunkPages = array_slice($staticPages, $startIndex, self::$chunkSize);

        $urls = collect($chunkPages)->map(function ($page) {
            return [
                'loc' => $page['url'],
                'lastmod' => $page['lastmod'],
                'changefreq' => $page['changefreq'],
                'priority' => $page['priority'],
            ];
        });

        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= view('sitemap.chunk', ['urls' => $urls])->render();

        return response($xml, 200)->header('Content-Type', 'application/xml');
    }

    public function robots()
    {
        $content = "User-agent: *\n";
        $content .= "Disallow: /admin\n";
        $content .= "Disallow: /login\n";
        $content .= "Disallow: /register\n";
        $content .= "Disallow: /password/reset\n";
        $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return response($content, 200)->header('Content-Type', 'text/plain');
    }
}
