<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\ViewHelpers;
use Illuminate\Support\Facades\View;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Make the getRandomCategoryColor function available in Blade templates
        Blade::directive('randomCategoryColor', function () {
            return "<?php \$categoryColor = \App\Helpers\ViewHelpers::getRandomCategoryColor(); ?>";
        });

        // Add a directive to check if a category exists
        Blade::directive('categoryExists', function ($expression) {
            return "<?php if(\App\Helpers\ViewHelpers::categoryExists($expression)): ?>";
        });

        Blade::directive('endcategoryExists', function () {
            return "<?php endif; ?>";
        });

        View::share('topCategories', \App\Helpers\ViewHelpers::getTopCategories());

        // Share navigation menu items using a view composer to avoid boot-time database queries
        View::composer(['components.frontend.header', 'components.frontend.footer'], function ($view) {
            $view->with('navigationMenuItems', \App\Helpers\ViewHelpers::getNavigationMenuItems());
        });
    }
}
