<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LatestNewsTitle;
use App\Services\NewsApiService;
use App\Services\NewsGeneratorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class NewsIntegrationController extends Controller
{
    /**
     * Display the news integration dashboard
     */
    public function index()
    {
        $stats = [
            'total' => LatestNewsTitle::count(),
            'processed' => LatestNewsTitle::where('is_processed', true)->count(),
            'generated' => LatestNewsTitle::where('is_generated', true)->count(),
            'pending' => LatestNewsTitle::readyForGeneration()->count(),
        ];

        return view('admin.news-integration.index', compact('stats'));
    }

    /**
     * Get latest news titles for DataTable
     */
    public function getData(Request $request)
    {
        $query = LatestNewsTitle::with('generatedNews')
            ->select(['id','country','language', 'title', 'source_published_at', 'is_processed', 'is_generated', 'created_at']);

        return DataTables::of($query)
            ->addColumn('status', function ($row) {
                if ($row->is_generated) {
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="material-icons text-xs mr-1">check_circle</i>Generated
                            </span>';
                } elseif ($row->is_processed) {
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="material-icons text-xs mr-1">pending</i>Ready
                            </span>';
                } else {
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="material-icons text-xs mr-1">schedule</i>Pending
                            </span>';
                }
            })
            ->addColumn('actions', function ($row) {
                $actions = '<div class="flex items-center justify-end space-x-2">';

                if ($row->is_generated && $row->generatedNews) {
                    $actions .= '<a href="' . route('admin.news.show', $row->generatedNews->id) . '"
                                   class="action-btn view" title="View Generated News">
                                   <i class="material-icons text-sm">visibility</i>
                                </a>';
                }

                if (!$row->is_generated && $row->is_processed) {
                    $actions .= '<button class="action-btn edit generate-single" data-id="' . $row->id . '" title="Generate Content">
                                   <i class="material-icons text-sm">auto_awesome</i>
                                </button>';
                }

                // Note: source_url column removed since API doesn't provide it

                $actions .= '</div>';
                return $actions;
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at->format('M d, Y H:i');
            })
            ->editColumn('title', function ($row) {
                return $row->title;
            })
            ->rawColumns(['status', 'actions'])
            ->make(true);
    }

    /**
     * Manually fetch latest news
     */
    public function fetchNews(Request $request)
    {
        try {
            // Get parameters from request (supports both POST data and query parameters)
            $limit = (int) ($request->input('limit', 10));
            $country = $request->input('country');
            $language = $request->input('language');
            $category = $request->input('category');

            // Log the fetch request with parameters
            Log::info('News fetch request initiated', [
                'limit' => $limit,
                'country' => $country,
                'language' => $language,
                'category' => $category,
                'source' => 'admin'
            ]);

            $newsApiService = app(NewsApiService::class);

            $stats = [
                'fetched' => 0,
                'new' => 0,
                'duplicates' => 0,
                'errors' => 0,
                'total' => LatestNewsTitle::count(),
                'processed' => LatestNewsTitle::where('is_processed', true)->count(),
                'generated' => LatestNewsTitle::where('is_generated', true)->count(),
                'pending' => LatestNewsTitle::where('is_processed', true)->where('is_generated', false)->count()
            ];

            // Pass all parameters to the service
            $params = [
                'limit' => $limit
            ];

            // Only add non-empty parameters
            if ($country) $params['country'] = $country;
            if ($language) $params['language'] = $language;
            if ($category) $params['category'] = $category;

            // Fetch articles from API with parameters
            $articles = $newsApiService->fetchLatestNews($params);
            $stats['fetched'] = count($articles);

            if (empty($articles)) {
                return response()->json([
                    'success' => true,
                    'message' => 'No articles fetched from API',
                    'stats' => $stats
                ]);
            }

            // Process each article
            $processed = 0;
            foreach ($articles as $article) {

                if ($processed >= $limit) {
                    break;
                }
                try {
                    // Check if article already exists
                    if (LatestNewsTitle::exists($article['title'])) {
                        $stats['duplicates']++;
                        continue;
                    }

                    // Create new record
                    LatestNewsTitle::create([
                        'title' => $article['title'],
                        'country' => $country ? $country : null,
                        'language' => $language ? $language : null,
                        'source_published_at' => $article['source_published_at'] ?? now(),
                        'api_response' => json_encode($article),
                        'is_processed' => true,
                    ]);

                    $stats['new']++;
                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error('Error processing article', [
                        'article' => $article,
                        'error' => $e->getMessage()
                    ]);
                }
                $processed++;
            }

            // Update stats after processing
            $stats['total'] = LatestNewsTitle::count();
            $stats['processed'] = LatestNewsTitle::where('is_processed', true)->count();
            $stats['generated'] = LatestNewsTitle::where('is_generated', true)->count();
            $stats['pending'] = LatestNewsTitle::where('is_processed', true)->where('is_generated', false)->count();

            // Log completion
            Log::info('News fetch completed successfully', [
                'stats' => $stats,
                'source' => 'admin'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'News fetch completed successfully',
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('News fetch failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'source' => 'admin'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manually generate news content
     */
    public function generateNews(Request $request)
    {
        try {
            $limit = $request->input('limit', 5);
            $newsGeneratorService = app(NewsGeneratorService::class);

            // Generate news articles using the service
            $results = $newsGeneratorService->generateNewsFromLatestTitles($limit);

            return response()->json([
                'success' => true,
                'message' => 'News generation completed successfully',
                'results' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate news: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * Generate content for a single news title
     */
    public function generateSingle(Request $request, $id)
    {
        try {
            $newsTitle = LatestNewsTitle::findOrFail($id);

            // Check if already generated
            if ($newsTitle->is_generated) {
                return response()->json([
                    'success' => false,
                    'message' => 'Content already generated for this news title'
                ], 400);
            }

            $newsGeneratorService = app(NewsGeneratorService::class);

            // Generate content for this specific news title
            $result = $newsGeneratorService->generateSingleNewsFromTitle($newsTitle);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Content generated successfully',
                    'news_id' => $result->id,
                    'news_title' => $result->title
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate content'
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get integration statistics
     */
    public function getStats()
    {
        try {
            $stats = [
                'total' => LatestNewsTitle::count(),
                'processed' => LatestNewsTitle::where('is_processed', true)->count(),
                'generated' => LatestNewsTitle::where('is_generated', true)->count(),
                'pending' => LatestNewsTitle::readyForGeneration()->count(),
                'recent_fetched' => LatestNewsTitle::where('created_at', '>=', now()->subHours(24))->count(),
                'recent_generated' => LatestNewsTitle::where('is_generated', true)
                    ->where('updated_at', '>=', now()->subHours(24))->count(),
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get stats: ' . $e->getMessage()
            ], 500);
        }
    }
}
