<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Services\EnvManager;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = [
            'general' => Setting::getByGroup('general'),
            'smtp' => Setting::getByGroup('smtp'),
            'email' => Setting::getByGroup('email'),
            'cache' => Setting::getByGroup('cache'),
            'api' => Setting::getByGroup('api'),
        ];

        // Get current Gemini API key from .env if not in database
        if (!isset($settings['api']['gemini_api_key'])) {
            $envGeminiKey = EnvManager::getEnvValue('GEMINI_API_KEY');
            if ($envGeminiKey) {
                $settings['api']['gemini_api_key'] = $envGeminiKey;
            }
        }

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'site_name' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'admin_email' => 'nullable|email|max:255',
            'timezone' => 'nullable|string|max:100',
            'smtp_host' => 'nullable|string|max:255',
            'smtp_port' => 'nullable|integer|min:1|max:65535',
            'smtp_username' => 'nullable|string|max:255',
            'smtp_password' => 'nullable|string|max:255',
            'smtp_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'nullable|email|max:255',
            'mail_from_name' => 'nullable|string|max:255',
            'gemini_api_key' => 'nullable|string|max:255',
        ]);

        // General Settings
        if ($request->has('site_name')) {
            Setting::set('site_name', $request->site_name, 'string', 'general', 'Website name');
        }
        if ($request->has('site_description')) {
            Setting::set('site_description', $request->site_description, 'string', 'general', 'Website description');
        }
        if ($request->has('admin_email')) {
            Setting::set('admin_email', $request->admin_email, 'string', 'general', 'Administrator email');
        }
        if ($request->has('timezone')) {
            Setting::set('timezone', $request->timezone, 'string', 'general', 'Default timezone');
        }

        // SMTP Settings (encrypted)
        if ($request->has('smtp_host')) {
            Setting::set('smtp_host', $request->smtp_host, 'string', 'smtp', 'SMTP server host', true);
        }
        if ($request->has('smtp_port')) {
            Setting::set('smtp_port', $request->smtp_port, 'integer', 'smtp', 'SMTP server port');
        }
        if ($request->has('smtp_username')) {
            Setting::set('smtp_username', $request->smtp_username, 'string', 'smtp', 'SMTP username', true);
        }
        if ($request->has('smtp_password')) {
            Setting::set('smtp_password', $request->smtp_password, 'string', 'smtp', 'SMTP password', true);
        }
        if ($request->has('smtp_encryption')) {
            Setting::set('smtp_encryption', $request->smtp_encryption, 'string', 'smtp', 'SMTP encryption');
        }

        // Email Settings
        if ($request->has('mail_from_address')) {
            Setting::set('mail_from_address', $request->mail_from_address, 'string', 'email', 'Default from email address');
        }
        if ($request->has('mail_from_name')) {
            Setting::set('mail_from_name', $request->mail_from_name, 'string', 'email', 'Default from name');
        }

        // API Settings
        if ($request->has('gemini_api_key')) {
            // Save to database (encrypted)
            Setting::set('gemini_api_key', $request->gemini_api_key, 'string', 'api', 'Gemini AI API Key', true);

            // Update .env file
            $envUpdated = EnvManager::updateEnv('GEMINI_API_KEY', $request->gemini_api_key);

            if (!$envUpdated) {
                return redirect()->back()->with('error', 'Settings saved to database but failed to update .env file. Please check file permissions.');
            }
        }

        return redirect()->route('admin.settings.index')
                        ->with('success', 'Settings updated successfully.');
    }

    /**
     * Test Gemini API key
     */
    public function testGeminiApi(Request $request)
    {
        $request->validate([
            'api_key' => 'required|string'
        ]);

        try {
            // Simple test to validate API key format
            $apiKey = $request->api_key;

            // Basic validation - Gemini API keys typically start with certain patterns
            if (strlen($apiKey) < 20) {
                return response()->json([
                    'success' => false,
                    'message' => 'API key appears to be too short. Please check your key.'
                ]);
            }

            // You can add more sophisticated validation here
            // For now, we'll just check if it's not empty and has reasonable length
            return response()->json([
                'success' => true,
                'message' => 'API key format appears valid. Please test with actual API calls to confirm functionality.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error testing API key: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Clear application cache
     */
    public function clearCache()
    {
        try {
            // Clear various caches
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');

            // Clear settings cache
            Setting::clearCache();

            return redirect()->route('admin.settings.index')
                            ->with('success', 'All caches cleared successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                            ->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // Temporarily set mail configuration
            $this->setMailConfig();

            Mail::raw('This is a test email from your admin panel.', function ($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('Test Email from Admin Panel');
            });

            return redirect()->route('admin.settings.index')
                            ->with('success', 'Test email sent successfully to ' . $request->test_email);
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                            ->with('error', 'Failed to send test email: ' . $e->getMessage());
        }
    }

    /**
     * Logout from all devices except current
     */
    public function logoutAllDevices(Request $request)
    {
        try {
            $user = Auth::user();
            $currentSessionId = session()->getId();
            $sessionDriver = config('session.driver');
            $loggedOutSessions = 0;
            $forceAll = $request->input('force_all', false);

            // Handle different session drivers
            if ($sessionDriver === 'database') {


                if ($forceAll) {
                    // NUCLEAR OPTION: Delete ALL sessions except current (all users)
                    $loggedOutSessions = DB::table(config('session.table', 'sessions'))
                        ->where('id', '!=', $currentSessionId)
                        ->count();

                    DB::table(config('session.table', 'sessions'))
                        ->where('id', '!=', $currentSessionId)
                        ->delete();

                    // Clear remember tokens for all users (extreme security measure)
                    User::whereNotNull('remember_token')->update(['remember_token' => null]);
                } else {
                    // NORMAL: Only logout current user's sessions (MINE ONLY)
                    $loggedOutSessions = DB::table(config('session.table', 'sessions'))
                        ->where('user_id', $user->id)
                        ->where('id', '!=', $currentSessionId)
                        ->count();

                    DB::table(config('session.table', 'sessions'))
                        ->where('user_id', $user->id)
                        ->where('id', '!=', $currentSessionId)
                        ->delete();

                    // Clear remember token for current user only
                    if ($user->remember_token) {
                        User::where('id', $user->id)->update(['remember_token' => null]);
                    }
                }
            } else {
                // For file-based sessions
                $loggedOutSessions = 1;
            }

            // Generate a new session ID for security
            session()->regenerate();

            if ($forceAll) {
                $message = "🚨 NUCLEAR LOGOUT: Terminated {$loggedOutSessions} session(s) from ALL users and devices. Only your current session remains active.";
            } else {
                $message = $loggedOutSessions > 0
                    ? "Successfully logged out from {$loggedOutSessions} of YOUR other device(s). Only YOUR current session remains active. Other users are unaffected."
                    : "No other active sessions found for your account. Your current session remains active.";
            }

            return redirect()->route('admin.settings.index')
                            ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                            ->with('error', 'Failed to logout from all devices: ' . $e->getMessage());
        }
    }

    /**
     * Emergency logout ALL users and clear entire sessions table
     * Only accessible by authenticated users
     */
    public function emergencyLogoutAll()
    {
        try {
            $currentSessionId = session()->getId();

            // Count total sessions before deletion
            $totalSessions = DB::table('sessions')->count();
            $totalUsers = DB::table('sessions')->distinct('user_id')->whereNotNull('user_id')->count();

            // EMERGENCY: Clear entire sessions table except current session
            DB::table('sessions')->where('id', '!=', $currentSessionId)->delete();

            // Clear all remember tokens for all users
            User::whereNotNull('remember_token')->update(['remember_token' => null]);

            // Regenerate current session for security
            session()->regenerate();

            $message = "🚨 EMERGENCY LOGOUT EXECUTED: Cleared entire sessions table. Logged out {$totalUsers} users from {$totalSessions} sessions. Only your session remains active.";

            return redirect()->route('admin.settings.index')
                            ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                            ->with('error', 'Emergency logout failed: ' . $e->getMessage());
        }
    }

    /**
     * Set mail configuration from settings
     */
    private function setMailConfig()
    {
        $smtpSettings = Setting::getByGroup('smtp');
        $emailSettings = Setting::getByGroup('email');

        if (!empty($smtpSettings['smtp_host'])) {
            Config::set('mail.mailers.smtp.host', $smtpSettings['smtp_host']);
        }
        if (!empty($smtpSettings['smtp_port'])) {
            Config::set('mail.mailers.smtp.port', $smtpSettings['smtp_port']);
        }
        if (!empty($smtpSettings['smtp_username'])) {
            Config::set('mail.mailers.smtp.username', $smtpSettings['smtp_username']);
        }
        if (!empty($smtpSettings['smtp_password'])) {
            Config::set('mail.mailers.smtp.password', $smtpSettings['smtp_password']);
        }
        if (!empty($smtpSettings['smtp_encryption'])) {
            Config::set('mail.mailers.smtp.encryption', $smtpSettings['smtp_encryption']);
        }
        if (!empty($emailSettings['mail_from_address'])) {
            Config::set('mail.from.address', $emailSettings['mail_from_address']);
        }
        if (!empty($emailSettings['mail_from_name'])) {
            Config::set('mail.from.name', $emailSettings['mail_from_name']);
        }
    }
}
