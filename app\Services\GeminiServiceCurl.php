<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Exception;

class GeminiServiceCurl
{
    protected $apiKey;
    protected $model;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->model = 'gemini-1.5-flash'; // Latest free tier model
        $this->baseUrl = 'https://generativelanguage.googleapis.com/v1/models/';
    }

    /**
     * Generate unique news content from a title
     *
     * @param string $originalTitle
     * @param string|null $originalDescription
     * @return array
     * @throws Exception
     */
    public function generateUniqueNews(string $originalTitle, ?string $originalDescription = null, ?string $originalLanguage = null, ?string $originalCountry = null): array
    {
        if (!$this->apiKey) {
            throw new Exception('Gemini API key not configured');
        }

        try {
            Log::info('Generating unique news content with Gemini (cURL)', [
                'original_title' => $originalTitle,
                'model' => $this->model
            ]);

            $prompt = $this->buildPrompt($originalTitle, $originalDescription, $originalLanguage, $originalCountry);

            $response = $this->makeApiCall($prompt);

            if (empty($response)) {
                throw new Exception('Gemini returned empty content');
            }

            $parsedContent = $this->parseGeneratedContent($response);

            Log::info('Successfully generated unique news content', [
                'original_title' => $originalTitle,
                'generated_title' => $parsedContent['title']
            ]);

            return $parsedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate content with Gemini', [
                'error' => $e->getMessage(),
                'original_title' => $originalTitle
            ]);
            throw $e;
        }
    }

    /**
     * Make API call using cURL
     */
    protected function makeApiCall(string $prompt): string
    {
        $url = $this->baseUrl . $this->model . ':generateContent?key=' . $this->apiKey;

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt
                        ]
                    ]
                ]
            ]
        ];

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
            CURLOPT_SSL_VERIFYPEER => env('GEMINI_SSL_VERIFY', true) !== false,
            CURLOPT_SSL_VERIFYHOST => env('GEMINI_SSL_VERIFY', true) !== false ? 2 : 0,
            CURLOPT_TIMEOUT => 60,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception('HTTP Error: ' . $httpCode . ' - ' . $response);
        }

        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON Parse Error: ' . json_last_error_msg());
        }

        if (!isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception('Unexpected response format from Gemini API');
        }

        return $responseData['candidates'][0]['content']['parts'][0]['text'];
    }

    /**
     * Build the prompt for Gemini
     */
    protected function buildPrompt(string $originalTitle, ?string $originalDescription = null, ?string $originalLanguage = null, ?string $originalCountry = null): string
    {
        $prompt = "You are a professional news writer and HTML content creator. Based on the following news information, create a completely unique and original news article that covers the same topic but with different wording, structure, and perspective to avoid copyright issues.\n\n";

        $prompt .= "Original Title: {$originalTitle}\n";
        if($originalLanguage){
            $prompt .= "Write the article in the following language: {$originalLanguage}.\n";
        }
        if($originalCountry){
            $prompt .= "Write the article with a focus on the following country: {$originalCountry}.\n";
        }
        if ($originalDescription) {
            $prompt .= "Original Description: {$originalDescription}\n";
        }

        $prompt .= "\nPlease generate:\n";
        $prompt .= "1. A unique title (different from the original but covering the same topic)\n";
        $prompt .= "2. An appropriate category for this news (e.g., Politics, Sports, Technology, Business, Health, Entertainment, Crime, Education, International, General)\n";
        $prompt .= "3. A comprehensive description/article content (minimum 600 words) formatted in HTML\n";
        $prompt .= "4. SEO-friendly meta title\n";
        $prompt .= "5. Meta description (150-160 characters)\n";
        $prompt .= "6. Meta keywords (comma-separated)\n";
        $prompt .= "7. OG title for social media\n";
        $prompt .= "8. OG description for social media\n\n";

        $prompt .= "HTML Content Requirements:\n";
        $prompt .= "- Use ONLY content HTML tags: headings (h2, h3), paragraphs (p), lists (ul, ol), and emphasis (strong, em)\n";
        $prompt .= "- DO NOT include structural HTML tags like <html>, <head>, <body>, <meta>, <title>, <DOCTYPE>, etc.\n";
        $prompt .= "- DO NOT include any CSS styles or <style> tags\n";
        $prompt .= "- DO NOT include any JavaScript or <script> tags\n";
        $prompt .= "- Include at least 3-4 subheadings (h2 or h3) to break up the content\n";
        $prompt .= "- Use bullet points or numbered lists where appropriate\n";
        $prompt .= "- Add emphasis with <strong> and <em> tags for important points\n";
        $prompt .= "- Structure content for easy reading with proper paragraph breaks\n";
        $prompt .= "- Include a compelling introduction and conclusion\n";
        $prompt .= "- Content should be ready for direct insertion into a webpage body\n\n";

        $prompt .= "General Requirements:\n";
        $prompt .= "- Content must be 100% original and unique\n";
        $prompt .= "- Maintain journalistic integrity and factual accuracy\n";
        $prompt .= "- Use engaging and professional language\n";
        $prompt .= "- Ensure content is SEO-optimized\n";
        $prompt .= "- Format the response as JSON with the following structure:\n\n";

        $prompt .= "{\n";
        $prompt .= '  "title": "Generated unique title",' . "\n";
        $prompt .= '  "category": "Appropriate category name",' . "\n";
        $prompt .= '  "description": "<p>HTML formatted article content with proper structure...</p>",' . "\n";
        $prompt .= '  "meta_title": "SEO meta title",' . "\n";
        $prompt .= '  "meta_description": "Meta description",' . "\n";
        $prompt .= '  "meta_keywords": "keyword1, keyword2, keyword3",' . "\n";
        $prompt .= '  "og_title": "Social media title",' . "\n";
        $prompt .= '  "og_description": "Social media description"' . "\n";
        $prompt .= "}\n\n";

        $prompt .= "IMPORTANT: The description field must contain ONLY content HTML tags that can be directly inserted into a webpage body using {!! \$content !!} in Laravel Blade templates.\n\n";
        $prompt .= "CRITICAL: DO NOT include <html>, <head>, <body>, <meta>, <title>, <DOCTYPE>, <style>, or <script> tags.\n\n";
        $prompt .= "Example HTML structure for description:\n";
        $prompt .= '<p>Engaging introduction paragraph...</p>' . "\n";
        $prompt .= '<h2>Main Topic Heading</h2>' . "\n";
        $prompt .= '<p>Content paragraph with <strong>important points</strong> and <em>emphasis</em>.</p>' . "\n";
        $prompt .= '<ul><li>Key point 1</li><li>Key point 2</li></ul>' . "\n";
        $prompt .= '<h3>Subtopic</h3>' . "\n";
        $prompt .= '<p>More detailed content...</p>' . "\n";
        $prompt .= '<p>Conclusion paragraph...</p>' . "\n\n";

        $prompt .= "FINAL VALIDATION:\n";
        $prompt .= "- Ensure JSON is valid and properly formatted with escaped quotes in HTML content\n";
        $prompt .= "- Double-check that description contains ONLY content tags (p, h2, h3, ul, li, strong, em)\n";
        $prompt .= "- Verify NO structural HTML tags are included (html, head, body, meta, title, style, script)\n";
        $prompt .= "- Make sure content is ready for direct insertion into webpage body";

        return $prompt;
    }

    /**
     * Parse the generated content from Gemini response
     */
    protected function parseGeneratedContent(string $content): array
    {
        // Try to extract JSON from the response
        $jsonStart = strpos($content, '{');
        $jsonEnd = strrpos($content, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonContent = substr($content, $jsonStart, $jsonEnd - $jsonStart + 1);
            $decoded = json_decode($jsonContent, true);

            if (json_last_error() === JSON_ERROR_NONE && $this->validateParsedContent($decoded)) {
                return $decoded;
            }
        }

        // Fallback: try to parse the entire content as JSON
        $decoded = json_decode($content, true);
        if (json_last_error() === JSON_ERROR_NONE && $this->validateParsedContent($decoded)) {
            return $decoded;
        }

        // If JSON parsing fails, create a structured response from plain text
        return $this->createFallbackStructure($content);
    }

    /**
     * Validate parsed content structure
     */
    protected function validateParsedContent(?array $content): bool
    {
        if (!is_array($content)) {
            return false;
        }

        $requiredFields = ['title', 'category', 'description'];
        foreach ($requiredFields as $field) {
            if (empty($content[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Create fallback structure when JSON parsing fails
     */
    protected function createFallbackStructure(string $content): array
    {
        // Extract title (usually the first line or after "Title:")
        $lines = explode("\n", $content);
        $title = '';
        $description = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            if (empty($title) && !empty($line)) {
                $title = $line;
            } else {
                $description .= $line . "\n";
            }
        }

        $title = preg_replace('/^(Title:|Generated Title:)/i', '', $title);
        $title = trim($title, ' :"');

        if (empty($title)) {
            $title = 'Generated News Article';
        }

        // Convert plain text to basic HTML structure
        $htmlDescription = $this->convertTextToHtml(trim($description) ?: $content);

        return [
            'title' => $title,
            'category' => 'General', // Fallback category
            'description' => $htmlDescription,
            'meta_title' => $title,
            'meta_description' => substr(strip_tags($htmlDescription), 0, 160),
            'meta_keywords' => '',
            'og_title' => $title,
            'og_description' => substr(strip_tags($htmlDescription), 0, 200),
        ];
    }

    /**
     * Convert plain text to basic HTML structure
     */
    protected function convertTextToHtml(string $text): string
    {
        // Split into paragraphs
        $paragraphs = explode("\n\n", $text);
        $html = '';

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (empty($paragraph)) continue;

            // Check if it looks like a heading (short line, possibly with colons)
            if (strlen($paragraph) < 100 && (strpos($paragraph, ':') !== false || preg_match('/^[A-Z][^.!?]*$/', $paragraph))) {
                $html .= '<h2>' . htmlspecialchars($paragraph) . '</h2>' . "\n";
            } else {
                // Regular paragraph
                $html .= '<p>' . htmlspecialchars($paragraph) . '</p>' . "\n";
            }
        }

        // If no HTML was generated, wrap the entire content in a paragraph
        if (empty($html)) {
            $html = '<p>' . htmlspecialchars($text) . '</p>';
        }

        return $html;
    }

    /**
     * Generate meta keywords based on content
     */
    public function generateMetaKeywords(string $title, string $description): string
    {
        if (!$this->apiKey) {
            return 'news, article, breaking news';
        }

        try {
            $prompt = "Based on the following news article, generate 5-8 relevant SEO keywords (comma-separated):\n\n";
            $prompt .= "Title: {$title}\n";
            $prompt .= "Content: " . substr($description, 0, 500) . "\n\n";
            $prompt .= "Return only the keywords, comma-separated, no additional text.";

            $response = $this->makeApiCall($prompt);
            return trim($response);
        } catch (Exception $e) {
            Log::warning('Failed to generate meta keywords', ['error' => $e->getMessage()]);
            return 'news, article, breaking news';
        }
    }
}
