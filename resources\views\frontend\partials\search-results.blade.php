@foreach($articles as $item)
<div class="py-2 px-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
    <div class="flex items-start space-x-3">
        <a href="{{ route('news.show', $item->slug) }}" class="flex-shrink-0 w-20  rounded overflow-hidden">
            <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-800">
                <img src="{{ asset('storage/' . $item->main_image) }}" 
                     alt="{{ $item->title }}" 
                     class="w-full h-full object-cover"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                    <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
            </div>
        </a>
        <div class="flex-1 min-w-0">
            <a href="{{ route('news.show', $item->slug) }}" class="block">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white overflow-hidden  line-clamp-2">
                    {{ $item->title }}
                </h3>
            </a>
            <p class="text-xs small text-gray-500 dark:text-gray-400">
                {{ Carbon\Carbon::parse($item->published_at)->format('M d, Y') }}
            </p>
        </div>
    </div>
</div>
@endforeach

@if(isset($totalCount) && $totalCount > count($articles))
    <div class="px-6 py-2">
        <a href="{{ route('search.page', ['query' => $query]) }}" class="text-black dark:text-white py-3 px-10 my-1 mx-1 rounded-md text-sm font-medium shadow-sm hover:shadow-md hover:opacity-90 transition-all duration-300 ease-in-out whitespace-nowrap inline-block hover:bg-gradient-to-r hover:from-blue-100 hover:to-transparent cursor-pointer w-full text-center">
            View all {{ $totalCount }} results
        </a>
    </div>
@endif