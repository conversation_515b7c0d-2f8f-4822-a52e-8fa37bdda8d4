<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\News;
use App\Models\LatestNewsTitle;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        // Get key statistics for the dashboard
        $stats = [
            // User Management
            'total_users' => User::count(),

            // News Management
            'total_news' => News::count(),
            'pending_news' => LatestNewsTitle::where(['is_generated' => 0,'is_processed' => 1])->count(),
            'published_news' => News::published()->count(),

            // Today's Activity
            'today_news' => News::whereDate('created_at', today())->count(),
            'today_generated' => LatestNewsTitle::whereDate('created_at', today())
                                                ->where('is_generated', true)
                                                ->count(),
            'today_pending' => LatestNewsTitle::whereDate('created_at', today())
                                  ->where(['is_generated' => 0,'is_processed' => 1])
                                  ->count(),
        ];

        // Get recent users
        $recent_users = User::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_users'));
    }
}
