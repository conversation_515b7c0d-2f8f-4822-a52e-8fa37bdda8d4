<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TagController extends Controller
{
    public function show($slug)
    {
        $tag = Tag::where('slug', $slug)->firstOrFail();
        $news = $tag->news()->published()->latest()->paginate(12);

        $meta_title = $tag->name . ' | ' . config('app.name') ?? $tag->title . ' | ' . config('app.name');
        $meta_description = $tag->meta_description ?? Str::limit(strip_tags($tag->content), 160);
        $meta_keywords = $tag->meta_keywords ?? '';
        $canonical = url($tag->slug);
        $og_title = $tag->og_title ?? $meta_title;
        $og_description = $tag->og_description ?? $meta_description;
        $og_type = $tag->og_type ?? 'article';
        $meta_image = $tag->og_image
            ? asset('storage/' . $tag->og_image)
            : asset('storage/' . $tag->main_image);
        $published_at = $tag->published_at ? $tag->published_at->format('Y-m-d\TH:i:sP') : null;
        $updated_at = $tag->updated_at ? $tag->updated_at->format('Y-m-d\TH:i:sP') : null;
        $robots_meta = $tag->robots_meta ?? 'index,follow';

        return view('frontend.tags.show', compact(
            'tag',
            'news',
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'robots_meta',
            'published_at',
            'updated_at'
        ));
    }
}
