<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();
            $table->string('author'); // API provider (Gemini, OpenAI, etc.)
            $table->text('key'); // API key (encrypted)
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->integer('today_count')->default(0); // How many times used today
            $table->integer('total_count')->default(0); // Total usage count
            $table->boolean('is_active')->default(false); // Currently used key for API calls
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['author', 'status']);
            $table->index(['is_active']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
