<header class="bg-white dark:bg-gray-900 shadow-md sticky top-0 z-50 transition-colors duration-200">
    <nav class="container mx-auto px-4 sm:px-6 lg:px-8" itemscope itemtype="http://www.schema.org/SiteNavigationElement">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a href="{{ url('/') }}" class="flex items-center space-x-2">
                    @if(file_exists(public_path('storage/images/logo-color.svg')) || file_exists(public_path('storage/images/logo-light.svg')))
                    <img src="{{asset('storage/images/logo-color.svg')}}"
                        alt="{{ config('app.name', 'Laravel') }} Logo"
                        class="w-25 rounded-lg object-cover logo-image dark:hidden">
                    <img src="{{asset('storage/images/logo-light.svg')}}"
                        alt="{{ config('app.name', 'Laravel') }} Logo"
                        class="w-25 rounded-lg object-cover logo-image hidden dark:block">
                    @else
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center logo-container">
                        <span class="text-white font-bold text-lg logo-text">{{ substr(config('app.name', 'Client'), 0, 1) }}</span>
                    </div>
                    @endif
                    <!-- <span class="text-xl font-bold text-gray-900 dark:text-white">{{ config('app.name', 'Laravel') }}</span> -->
                </a>
            </div>

            <!-- Desktop Mega Menu Navigation -->
            <div class="hidden lg:block">
                <div class="ml-6 flex items-center space-x-1">
                    @foreach($navigationMenuItems as $key => $menuItem)
                    @if($menuItem['exists'])
                    @if($menuItem['type'] === 'dropdown')
                    <!-- Dropdown Menu -->
                    <div class="relative group" data-mega-menu="{{ $key }}">
                        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 group flex items-center" data-mega-trigger>
                            <span>{{ $menuItem['name'] }}</span>
                            <svg class="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <meta itemprop="name" content="{{ $menuItem['name'] }}">
                        <meta itemprop="url" content="#{{ $key }}">
                        <div class="absolute top-full mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transition-all duration-200 ease-out z-50 py-2 group-hover:opacity-100 group-hover:visible" data-mega-content style="max-width: calc(100vw - 2rem); left: 50%; transform: translateX(-50%) translateY(8px); width: 200px;">
                            <div class="space-y-1">
                                @foreach($menuItem['dropdown_items'] as $dropdownItem)
                                <a href="{{ $dropdownItem['url'] }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    {{ $dropdownItem['name'] }}
                                </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @else
                    <!-- Regular Link -->
                    <a href="{{ $menuItem['url'] }}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 {{ (request()->url() == url($menuItem['url'])) ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : '' }}">
                        {{ $menuItem['name'] }}
                    </a>
                    <meta itemprop="name" content="{{ $menuItem['name'] }}">
                    <meta itemprop="url" content="{{ $menuItem['url'] }}">
                    @endif
                    @endif
                    @endforeach
                </div>
            </div>



            <!-- Theme Toggle & Search Button (right side on mobile) -->
            <div class="flex items-center space-x-3">
                <!-- Theme Toggle -->
                <div class="relative" data-theme-dropdown>
                    <button data-theme-dropdown-button class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-100 dark:focus:ring-red-900 cursor-pointer" aria-label="Toggle theme">
                        <!-- Light mode icon -->
                        <svg id="theme-icon-light" class="h-5 w-5 text-gray-600 dark:text-gray-400 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <!-- Dark mode icon -->
                        <svg id="theme-icon-dark" class="h-5 w-5 text-gray-600 dark:text-gray-400 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                        <!-- System mode icon -->
                        <svg id="theme-icon-system" class="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </button>
                    <div data-theme-dropdown-menu class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 transition-all duration-100">
                        <button data-theme-option="light" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            Light
                        </button>
                        <button data-theme-option="dark" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                            Dark
                        </button>
                        <button data-theme-option="system" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            System
                        </button>
                    </div>
                </div>

                <!-- Search Button -->
                <button type="button"
                    id="search-button"
                    class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-100 dark:focus:ring-red-900 cursor-pointer"
                    aria-label="Search">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-gray-600 dark:text-gray-400">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                    </svg>
                </button>

                <!-- Mobile menu button (rightmost position) -->
                <div class="lg:hidden">
                    <button type="button"
                        id="mobile-nav-button"
                        class="group relative flex cursor-pointer items-center justify-center rounded-full bg-gray-50 dark:bg-gray-800 p-3 ring-1 ring-gray-900/5 dark:ring-gray-100/5 transition duration-300 ease-in-out hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none cursor-pointer"
                        aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <span class="relative h-3.5 w-4">
                            <span class="absolute block h-0.5 rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100 left-0 top-0 w-full" id="menu-line-1"></span>
                            <span class="absolute left-0 top-1.5 block h-0.5 w-full rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100" id="menu-line-2"></span>
                            <span class="absolute left-0 top-1.5 block h-0.5 w-full rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100" id="menu-line-3"></span>
                            <span class="absolute block h-0.5 rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100 left-0 top-3 w-full" id="menu-line-4"></span>
                        </span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Slide Menu Overlay -->
        <div id="mobile-menu-overlay" class="fixed inset-0 z-40 bg-[rgba(0,0,0,0.5)] transition-opacity duration-300 opacity-0 pointer-events-none lg:hidden"></div>

        <!-- Mobile Left Slide Menu -->
        <div id="mobile-slide-menu" class="fixed top-0 left-0 z-50 h-full w-72 bg-white dark:bg-gray-900 shadow-xl transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden flex flex-col">
            <!-- Mobile Menu Header -->
            <div class="bg-gradient-to-r from-blue-100 to-transparent dark:from-blue-900/20 dark:to-transparent flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                        <span class="text-white font-bold text-sm">{{ substr(config('app.name', 'Client'), 0, 1) }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-bold text-gray-900 dark:text-white">{{ config('app.name', 'Laravel') }}</span>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Menu</p>
                    </div>
                </div>
                <button id="mobile-menu-close"
                    class="p-1.5 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                    aria-label="Close mobile menu">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu Content -->
            <div class="flex-1 overflow-y-auto overflow-x-hidden">
                <nav class="px-3 py-4 space-y-1 min-h-full">
                    @foreach($navigationMenuItems as $key => $menuItem)
                    @if($menuItem['exists'])
                    @if($menuItem['type'] === 'dropdown')
                    <!-- Mobile Dropdown Section -->
                    <div class="">
                        <button class="cursor-pointer flex items-center justify-between w-full px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-gray-50 hover:to-transparent dark:hover:from-gray-800 dark:hover:to-transparent" data-section="{{ $key }}">
                            <span class="flex items-center space-x-2">
                                <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $menuItem['icon'] }}" />
                                </svg>
                                <span>{{ $menuItem['name'] }}</span>
                            </span>
                            <svg class="h-3 w-3 flex-shrink-0 transition-transform duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out ml-3" data-content="{{ $key }}">
                            <div class="pl-4 space-y-0.5 py-2">
                                @foreach($menuItem['dropdown_items'] as $dropdownItem)
                                <a href="{{ $dropdownItem['url'] }}" class="block space-x-2 px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200">
                                    {{ $dropdownItem['name'] }}
                                </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @else
                    <!-- Mobile Regular Link -->
                    <a href="{{ $menuItem['url'] }}" class="flex items-center space-x-2 px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-blue-100 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent {{ (request()->url() == url($menuItem['url'])) ? 'bg-gradient-to-r from-blue-100 to-blue-50/50 dark:from-blue-900/30 dark:to-blue-900/10 text-blue-600 dark:text-blue-400' : '' }}">
                        <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $menuItem['icon'] }}" />
                        </svg>
                        <span>{{ $menuItem['name'] }}</span>
                    </a>
                    @endif
                    @endif
                    @endforeach


                </nav>
            </div>
        </div>
    </nav>

    <!-- Search Popup Modal -->
    <div id="search-modal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!-- Background overlay -->
        <div class="flex min-h-screen items-center justify-center px-4 py-6 text-center">
            <div class="fixed inset-0 bg-black/50 dark:bg-black/70 transition-opacity duration-300" aria-hidden="true" id="search-overlay"></div>

            <!-- Modal panel -->
            <div id="search-modal-panel" class="inline-block w-full max-w-2xl transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all duration-300 ease-out -translate-y-4 opacity-0">
                <!-- Modal header -->
                <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="modal-title">Search News</h3>
                    <button type="button"
                        id="close-search-modal"
                        class="rounded-md bg-white dark:bg-gray-800 text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 cursor-pointer">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Search form -->
                <div class="sm:px-6 sm:py-4 p-0">
                    <form id="search-form">
                        <div class="relative ">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-gray-400">
                                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <input type="text"
                                id="search-input"
                                class="block w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 pl-10 pr-4 py-3 text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 "
                                placeholder="Search for articles, topics, or keywords..."
                                autocomplete="off">
                        </div>
                    </form>
                </div>

                <!-- Search results -->
                <div id="search-results" class="max-h-96 overflow-y-auto border-t border-gray-200 dark:border-gray-700">
                    <!-- Default state -->
                    <div id="search-placeholder" class="px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Start typing to search</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Search through our articles and find what you're looking for.</p>
                    </div>

                    <!-- Loading state -->
                    <div id="search-loading" class="hidden px-6 py-8 text-center">
                        <div class="flex flex-col items-center">
                            <!-- Animated loading spinner -->
                            <div class="relative">
                                <div class="w-12 h-12 border-4 border-gray-200 dark:border-gray-600 rounded-full">
                                    <div class="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-blue-500 dark:border-t-blue-400 rounded-full search-loading-spinner"></div>
                                </div>
                            </div>
                            <h3 class="mt-4 text-sm font-medium text-gray-900 dark:text-white">Searching...</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Please wait while we find relevant articles.</p>
                        </div>
                    </div>

                    <!-- Search results will be populated here -->
                    <div id="search-results-list" class="hidden">
                        <!-- Results will be dynamically inserted here -->
                    </div>

                    <!-- No results state -->
                    <div id="no-results" class="hidden px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.137 0-4.146-.832-5.636-2.364M12 15l3.031-3.031" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No results found</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Try adjusting your search terms or browse our categories.</p>
                    </div>
                </div>

                <!-- Quick links -->
                <div class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 px-6 py-4">
                    <h4 class="text-xs font-medium uppercase tracking-wide text-gray-500 dark:text-gray-400">Popular Categories</h4>
                    @if(isset($topCategories) && $topCategories->isNotEmpty())
                    <div class="mt-2 flex flex-wrap gap-2">
                        @foreach($topCategories as $category)
                        <a href="{{ route('category.show', $category->slug) }}" class="inline-flex items-center rounded-full bg-white dark:bg-gray-600 px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors duration-200">
                            {{ $category->name }}
                        </a>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</header>