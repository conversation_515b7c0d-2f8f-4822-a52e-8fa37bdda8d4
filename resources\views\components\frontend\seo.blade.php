    <title>{{ $meta_title }}</title>
    <meta name="description" content="{{ $meta_description }}">
    <meta name="keywords" content="{{ $meta_keywords ?? 'news, breaking news, headlines' }}">
    <link rel="canonical" href="{{ $canonical ?? url()->current() }}">
    <meta name="robots" content="{{ $robots_meta ?? 'index,follow' }}">
    <meta property="og:title" content="{{ $meta_title }}">
    <meta property="og:description" content="{{ $meta_description }}">
    <meta property="og:image" content="{{ $meta_image }}">
    <meta property="og:url" content="{{ $canonical ?? url()->current() }}">
    <meta property="og:type" content="{{ $og_type ?? 'website' }}">
    <meta property="og:site_name" content="{{ config('app.name', 'News') }}">
    <meta name="twitter:card" content="{{ $twitter_card ?? 'summary_large_image' }}">
    <meta name="twitter:title" content="{{ $twitter_title ?? $meta_title }}">
    <meta name="twitter:description" content="{{ $twitter_description ?? $meta_description }}">
    <meta name="twitter:image" content="{{ $twitter_image ?? $meta_image }}">
    <meta name="twitter:url" content="{{ $canonical ?? url()->current() }}">
    @if(isset($isSingleNews))
    <meta property="article:published_time" content="{{$published_at}}">
    <meta property="article:modified_time" content="{{$updated_at}}">
    <meta property="article:author" content="LTNews">
    <meta property="article:section" content="{{$categories}}">
    <meta property="article:tag" content="{{$tags}}">
    @endif
    <meta name="theme-color" content="#ffffff">
    <link rel="icon" href="{{ asset('storage/images/logo-color.svg') }}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{{ asset('storage/images/logo-color.svg') }}">
    <link rel="manifest" href="{{ asset('manifest.json') }}">
    <link rel="mask-icon" href="{{ asset('storage/images/logo-color.svg') }}" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="{{ asset('browserconfig.xml') }}">
    <meta name="google-site-verification" content="{{ config('services.google.site_verification') }}">
    <meta name="yandex-verification" content="{{ config('services.yandex.verification') }}">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ config('app.name', 'News') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="preload" href="{{ asset('fonts/roboto/Roboto-Regular.woff2') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('fonts/roboto/Roboto-Medium.woff2') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('fonts/roboto/Roboto-Bold.woff2') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('fonts/roboto/Roboto-Italic.woff2') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('fonts/roboto/Roboto-MediumItalic.woff2') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('fonts/roboto/Roboto-BoldItalic.woff2') }}" as="font" type="font/woff2" crossorigin>
     @if(isset($featuredNews) && $featuredNews->isNotEmpty() && $featuredNews[0]->main_image)
    <link rel="preload" as="image" href="{{ asset('storage/' . $featuredNews[0]->main_image) }}"  fetchpriority="high" type="image/svg+xml">
    @endif