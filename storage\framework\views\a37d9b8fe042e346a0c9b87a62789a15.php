<?php $__env->startSection('title', 'Pending News Approval'); ?>

<?php
    $pageTitle = 'Pending News Approval';
    $pageDescription = 'Review and approve AI-generated news content';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Pending News', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
        <button id="bulk-approve-btn" class="material-button material-button-md material-button-primary flex items-center" disabled>
            <i class="material-icons mr-2">check_circle</i>
            Bulk Approve
        </button>
        <button id="refresh-stats" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">refresh</i>
            Refresh
        </button>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Pending Approval -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-yellow-600 text-lg">pending</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Pending Approval</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-pending">0</p>
            </div>
        </div>
    </div>

    <!-- Approved Today -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-green-600 text-lg">check_circle</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Approved Today</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-approved">0</p>
            </div>
        </div>
    </div>

    <!-- Rejected Today -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-red-600 text-lg">cancel</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Rejected Today</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-rejected">0</p>
            </div>
        </div>
    </div>

    <!-- Total Generated -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-blue-600 text-lg">auto_awesome</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Generated</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-total">0</p>
            </div>
        </div>
    </div>
</div>

<!-- Pending News DataTable -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- DataTable Container -->
    <div class="p-6">
        <!-- Custom Controls -->
        <div class="mb-6 flex justify-end">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="material-icons text-gray-400 text-sm">search</i>
                    </div>
                    <input type="text" id="search-input" placeholder="Search pending news..."
                           class="material-input pl-10 w-64">
                </div>
                <button id="refresh-table" class="material-button material-button-sm material-button-secondary p-2" title="Refresh">
                    <i class="material-icons text-sm">refresh</i>
                </button>
            </div>
        </div>

        <!-- Table -->
        <div>
            <table id="pending-news-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">News</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categories</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tags</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTables will populate this -->
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                    <span class="text-sm text-gray-700">Show</span>
                    <select id="page-length" class="w-16">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-sm text-gray-700">entries</span>
                </div>
                <div id="table-info" class="text-sm text-gray-700 mb-4 sm:mb-0"></div>
                <div id="pending-news-pagination" class="w-full sm:w-auto"></div>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejection-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reject News Article</h3>
                <button id="close-rejection-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <form id="rejection-form">
                <div class="mb-4">
                    <label for="rejection-reason" class="block text-sm font-medium text-gray-700 mb-2">
                        Rejection Reason <span class="text-red-500">*</span>
                    </label>
                    <textarea id="rejection-reason" name="reason" rows="4" required
                        class="material-textarea"
                        placeholder="Please provide a reason for rejecting this article..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-rejection" class="material-button material-button-sm material-button-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="material-button material-button-sm material-button-danger">
                        Reject Article
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .action-btn {
        @apply inline-flex items-center justify-center w-8 h-8 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
    }
    .action-btn.approve {
        @apply border-green-300 text-green-700 hover:bg-green-50 focus:ring-green-500;
    }
    .action-btn.reject {
        @apply border-red-300 text-red-700 hover:bg-red-50 focus:ring-red-500;
    }
    .action-btn.view {
        @apply border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500;
    }
    .action-btn.edit {
        @apply border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    let currentRejectId = null;
    
    // Initialize DataTable
    let table = $('#pending-news-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '<?php echo e(route("admin.pending-news.index")); ?>',
            type: 'GET'
        },
        columns: [
            {
                data: 'id',
                name: 'id',
                orderable: false,
                searchable: false,
                render: function(data) {
                    return '<input type="checkbox" class="row-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" value="' + data + '">';
                }
            },
            {
                data: 'news',
                name: 'title',
                orderable: true,
                searchable: true
            },
            {
                data: 'categories',
                name: 'categories',
                orderable: false,
                searchable: false
            },
            {
                data: 'tags',
                name: 'tags',
                orderable: false,
                searchable: false
            },
            {
                data: 'approval_status',
                name: 'approval_status',
                orderable: true,
                searchable: false
            },
            {
                data: 'created_at',
                name: 'created_at',
                orderable: true,
                searchable: false
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-right'
            }
        ],
        order: [[5, 'desc']], // Sort by created_at desc by default
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div>Loading data...</div>',
            paginate: {
                previous: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-left"><polyline points="15 18 9 12 15 6"></polyline></svg>',
                next: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg>'
            },
            info: "Showing _START_ to _END_ of _TOTAL_ entries"
        },
        dom: 'rtip',
        responsive: true,
        drawCallback: function(settings) {
            // Move pagination to custom container
            const $pagination = $('.dataTables_paginate').detach();
            $('#pending-news-pagination').empty().append($pagination);

            // Move info to custom container
            const $info = $('.dataTables_info').detach();
            $('#table-info').empty().append($info);
            
            // Set the current page length in the dropdown
            $('#page-length').val(table.page.len());
            
            // Add custom class to pagination buttons
            $('.paginate_button').addClass('custom-paginate-button');
            
            // Update bulk action button state
            updateBulkActionButton();
        }
    });

    // Custom search functionality
    $('#search-input').on('keyup', function() {
        table.search($(this).val()).draw();
    });

    // Custom page length functionality
    $('#page-length').on('change', function() {
        table.page.len(parseInt($(this).val())).draw();
    });

    // Refresh table functionality
    $('#refresh-table').on('click', function() {
        table.ajax.reload();
        loadStats();
    });

    // Select all functionality
    $('#select-all').on('change', function() {
        $('.row-checkbox').prop('checked', this.checked);
        updateBulkActionButton();
    });

    // Individual checkbox functionality
    $(document).on('change', '.row-checkbox', function() {
        updateBulkActionButton();
        
        // Update select all checkbox
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update bulk action button state
    function updateBulkActionButton() {
        const checkedCount = $('.row-checkbox:checked').length;
        $('#bulk-approve-btn').prop('disabled', checkedCount === 0);

        // Update button text with count
        if (checkedCount > 0) {
            $('#bulk-approve-btn').html('<i class="material-icons mr-2">check_circle</i>Approve (' + checkedCount + ')');
        } else {
            $('#bulk-approve-btn').html('<i class="material-icons mr-2">check_circle</i>Bulk Approve');
        }
    }

    // Load statistics
    function loadStats() {
        $.ajax({
            url: '<?php echo e(route("admin.pending-news.stats")); ?>',
            method: 'GET',
            success: function(data) {
                $('#stat-pending').text(data.pending);
                $('#stat-approved').text(data.approved_today);
                $('#stat-rejected').text(data.rejected_today);
                $('#stat-total').text(data.total_generated);
            },
            error: function(xhr) {
                console.error('Failed to load statistics:', xhr);
            }
        });
    }

    // Refresh stats
    $('#refresh-stats').on('click', function() {
        loadStats();
    });

    // Load initial stats
    loadStats();
});

// Approve news function
window.approveNews = function(id) {
    Swal.fire({
        title: 'Approve News Article?',
        text: 'This will make the article live on your website.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#10b981',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, Approve',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route("admin.pending-news.approve", ":id")); ?>'.replace(':id', id),
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    Swal.fire('Approved!', response.message, 'success');
                    $('#pending-news-table').DataTable().ajax.reload();
                    loadStats();
                },
                error: function(xhr) {
                    const message = xhr.responseJSON?.message || 'Failed to approve news';
                    Swal.fire('Error!', message, 'error');
                }
            });
        }
    });
};

// Reject news function
window.rejectNews = function(id) {
    currentRejectId = id;
    $('#rejection-modal').removeClass('hidden');
    $('#rejection-reason').val('').focus();
};

// Close rejection modal
$('#close-rejection-modal, #cancel-rejection').on('click', function() {
    $('#rejection-modal').addClass('hidden');
    $('#rejection-reason').val('');
    currentRejectId = null;
});

// Close modal when clicking outside
$('#rejection-modal').on('click', function(e) {
    if (e.target === this) {
        $(this).addClass('hidden');
        $('#rejection-reason').val('');
        currentRejectId = null;
    }
});

// Escape key to close modal
$(document).on('keydown', function(e) {
    if (e.key === 'Escape' && !$('#rejection-modal').hasClass('hidden')) {
        $('#rejection-modal').addClass('hidden');
        $('#rejection-reason').val('');
        currentRejectId = null;
    }
});

// Handle rejection form submission
$('#rejection-form').on('submit', function(e) {
    e.preventDefault();
    
    if (!currentRejectId) return;
    
    const reason = $('#rejection-reason').val().trim();
    if (!reason) {
        Swal.fire('Error!', 'Please provide a rejection reason', 'error');
        return;
    }
    
    $.ajax({
        url: '<?php echo e(route("admin.pending-news.reject", ":id")); ?>'.replace(':id', currentRejectId),
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            reason: reason
        },
        success: function(response) {
            $('#rejection-modal').addClass('hidden');
            Swal.fire('Rejected!', response.message, 'success');
            $('#pending-news-table').DataTable().ajax.reload();
            loadStats();
            currentRejectId = null;
        },
        error: function(xhr) {
            const message = xhr.responseJSON?.message || 'Failed to reject news';
            Swal.fire('Error!', message, 'error');
        }
    });
});

// Bulk approve functionality
$('#bulk-approve-btn').on('click', function() {
    const selectedIds = $('.row-checkbox:checked').map(function() {
        return $(this).val();
    }).get();
    
    if (selectedIds.length === 0) return;
    
    Swal.fire({
        title: 'Bulk Approve News?',
        text: `This will approve ${selectedIds.length} news articles and make them live.`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#10b981',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, Approve All',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route("admin.pending-news.bulk-approve")); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    ids: selectedIds
                },
                success: function(response) {
                    Swal.fire('Approved!', response.message, 'success');
                    $('#pending-news-table').DataTable().ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkActionButton();
                    loadStats();
                },
                error: function(xhr) {
                    const message = xhr.responseJSON?.message || 'Failed to approve news items';
                    Swal.fire('Error!', message, 'error');
                }
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/pending-news/index.blade.php ENDPATH**/ ?>