@extends('layouts.admin')

@section('title', 'API Key Details')

@section('content')
<!-- <PERSON> Header -->
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8">
    <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">API Key Details</h1>
        <p class="text-gray-600">View details for {{ $providers[$apiKey->provider] ?? ucfirst($apiKey->provider) }} API key</p>
    </div>
    <div class="flex items-center space-x-3 mt-4 sm:mt-0">
        <a href="{{ route('admin.api-keys.edit', $apiKey) }}" 
           class="material-button material-button-primary">
            <i class="material-icons text-sm mr-2">edit</i>
            Edit API Key
        </a>
        <a href="{{ route('admin.api-keys.index') }}" 
           class="material-button material-button-secondary">
            <i class="material-icons text-sm mr-2">arrow_back</i>
            Back to API Keys
        </a>
    </div>
</div>

<!-- API Key Details -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Information -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">API Key Information</h3>
            </div>
            <div class="p-6 space-y-6">
                <!-- Provider -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">API Provider</label>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="material-icons text-blue-600">business</i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{{ $providers[$apiKey->provider] ?? ucfirst($apiKey->provider) }}</div>
                            <div class="text-sm text-gray-500">{{ ucfirst($apiKey->provider) }}</div>
                        </div>
                    </div>
                </div>

                <!-- Owner -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Key Owner</label>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="material-icons text-green-600">person</i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{{ $apiKey->owner }}</div>
                            <div class="text-sm text-gray-500">Key Owner</div>
                        </div>
                    </div>
                </div>

                <!-- API Key -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <code class="text-sm font-mono text-gray-900" id="api-key-display">{{ $apiKey->masked_key }}</code>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="toggle-key" class="text-sm text-blue-600 hover:text-blue-800">
                                    <i class="material-icons text-sm mr-1">visibility</i>
                                    Show
                                </button>
                                <button type="button" id="copy-key" class="text-sm text-blue-600 hover:text-blue-800">
                                    <i class="material-icons text-sm mr-1">content_copy</i>
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        <i class="material-icons text-xs mr-1">security</i>
                        API key is encrypted and stored securely
                    </p>
                </div>

                <!-- Status -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <div class="flex items-center">
                        @if($apiKey->status === 'active')
                            <span class="status-badge verified">Active</span>
                        @else
                            <span class="status-badge unverified">Inactive</span>
                        @endif
                    </div>
                </div>

                <!-- Currently Used -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Currently Used</label>
                    <div class="flex items-center">
                        @if($apiKey->is_active)
                            <span class="status-badge verified">Yes - This key is currently being used for API calls</span>
                        @else
                            <span class="status-badge unverified">No - This key is not currently being used</span>
                        @endif
                    </div>
                </div>

                <!-- Timestamps -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Created</label>
                        <div class="text-sm text-gray-900">{{ $apiKey->created_at->format('M d, Y \a\t g:i A') }}</div>
                        <div class="text-xs text-gray-500">{{ $apiKey->created_at->diffForHumans() }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Updated</label>
                        <div class="text-sm text-gray-900">{{ $apiKey->updated_at->format('M d, Y \a\t g:i A') }}</div>
                        <div class="text-xs text-gray-500">{{ $apiKey->updated_at->diffForHumans() }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Statistics -->
    <div class="lg:col-span-1">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Usage Statistics</h3>
            </div>
            <div class="p-6 space-y-6">
                <!-- Today's Usage -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="material-icons text-orange-600 text-2xl">today</i>
                    </div>
                    <div class="text-3xl font-bold text-gray-900">{{ number_format($apiKey->today_count) }}</div>
                    <div class="text-sm text-gray-500">Requests Today</div>
                </div>

                <hr class="border-gray-200">

                <!-- Total Usage -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="material-icons text-purple-600 text-2xl">analytics</i>
                    </div>
                    <div class="text-3xl font-bold text-gray-900">{{ number_format($apiKey->total_count) }}</div>
                    <div class="text-sm text-gray-500">Total Requests</div>
                </div>

                <hr class="border-gray-200">

                <!-- Average Daily Usage -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="material-icons text-green-600 text-2xl">trending_up</i>
                    </div>
                    @php
                        $daysSinceCreated = max(1, $apiKey->created_at->diffInDays(now()) ?: 1);
                        $averageDaily = round($apiKey->total_count / $daysSinceCreated, 1);
                    @endphp
                    <div class="text-3xl font-bold text-gray-900">{{ number_format($averageDaily, 1) }}</div>
                    <div class="text-sm text-gray-500">Average Daily</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mt-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="p-6 space-y-3">
                @if(!$apiKey->is_active && $apiKey->status === 'active')
                    <button type="button" id="set-active" class="w-full material-button material-button-primary">
                        <i class="material-icons text-sm mr-2">play_arrow</i>
                        Set as Active Key
                    </button>
                @endif
                
                <a href="{{ route('admin.api-keys.edit', $apiKey) }}" 
                   class="w-full material-button material-button-secondary">
                    <i class="material-icons text-sm mr-2">edit</i>
                    Edit API Key
                </a>
                
                <button type="button" id="delete-key" class="w-full material-button material-button-danger">
                    <i class="material-icons text-sm mr-2">delete</i>
                    Delete API Key
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- jQuery (load only if not already loaded) -->
<script>
if (typeof jQuery === 'undefined') {
    document.write('<script src="https://code.jquery.com/jquery-3.7.1.min.js"><\/script>');
}
</script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Wait for jQuery to be available
(function checkjQuery() {
    if (typeof jQuery !== 'undefined') {
        $(document).ready(function() {
    let keyVisible = false;
    const maskedKey = '{{ $apiKey->masked_key }}';
    const fullKey = '{{ $apiKey->key }}';

    // Toggle key visibility
    $('#toggle-key').on('click', function() {
        if (keyVisible) {
            $('#api-key-display').text(maskedKey);
            $(this).html('<i class="material-icons text-sm mr-1">visibility</i>Show');
            keyVisible = false;
        } else {
            $('#api-key-display').text(fullKey);
            $(this).html('<i class="material-icons text-sm mr-1">visibility_off</i>Hide');
            keyVisible = true;
        }
    });

    // Copy key to clipboard
    $('#copy-key').on('click', function() {
        const textToCopy = keyVisible ? fullKey : maskedKey;
        navigator.clipboard.writeText(fullKey).then(function() {
            Swal.fire({
                title: 'Copied!',
                text: 'API key copied to clipboard',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        });
    });

    // Set as active key
    $('#set-active').on('click', function() {
        Swal.fire({
            title: 'Set as Active Key?',
            text: 'This will deactivate other keys for the same provider.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, set as active'
        }).then((result) => {
            if (result.isConfirmed) {
                // This would need a route to handle setting active
                Swal.fire('Success!', 'API key set as active.', 'success');
            }
        });
    });

    // Delete key
    $('#delete-key').on('click', function() {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("admin.api-keys.destroy", $apiKey) }}',
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Deleted!', response.message, 'success').then(() => {
                                window.location.href = '{{ route("admin.api-keys.index") }}';
                            });
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Something went wrong.', 'error');
                    }
                });
            }
        });
        });
    } else {
        setTimeout(checkjQuery, 50);
    }
})();
</script>
@endpush
