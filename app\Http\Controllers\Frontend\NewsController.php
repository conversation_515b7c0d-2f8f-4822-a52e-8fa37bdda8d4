<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsController extends Controller
{
    public function show($slug)
    {
        $news = News::where('slug', $slug)
            ->published()
            ->firstOrFail();

        // Increment view count
        $news->increment('view_count');

        // Get related news from same categories
        $relatedNews = News::published()
            ->whereHas('categories', function ($query) use ($news) {
                $query->whereIn('categories.id', $news->categories->pluck('id'));
            })
            ->where('id', '!=', $news->id)
            ->latest()
            ->limit(3)
            ->get();

        $meta_title = $news->meta_title . ' | ' . config('app.name') ?? $news->title . ' | ' . config('app.name');
        $meta_description = $news->meta_description ?? Str::limit(strip_tags($news->content), 160);
        $meta_keywords = $news->meta_keywords ?? '';
        $canonical = url($news->slug);
        $og_title = $news->og_title ?? $meta_title;
        $og_description = $news->og_description ?? $meta_description;
        $og_type = $news->og_type ?? 'article';
        $meta_image = $news->og_image
            ? asset('storage/' . $news->og_image)
            : asset('storage/' . $news->main_image);
        $published_at = $news->published_at ? $news->published_at->format('Y-m-d\TH:i:sP') : null;
        $updated_at = $news->updated_at ? $news->updated_at->format('Y-m-d\TH:i:sP') : null;
        $robots_meta = $news->robots_meta ?? 'index,follow';
        $tags = $news->tags?->pluck('name')->implode(', ') ?? '';
        $categories = $news->categories?->pluck('name')->implode(', ') ?? '';
        $twitter_card = 'summary_large_image';
        $twitter_title = $og_title;
        $twitter_description = $og_description;
        $twitter_image = $meta_image;
        $isSingleNews = true;

        return view('frontend.news.show', compact(
            'news',
            'relatedNews',
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'robots_meta',
            'twitter_card',
            'twitter_title',
            'twitter_description',
            'twitter_image',
            'isSingleNews',
            'tags',
            'categories',
            'published_at',
            'updated_at'
        ));
    }
}
