<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="light">
<head>
    <!-- Force light mode for admin panel by adding class="light" to html tag -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Admin @yield('title', 'Dashboard')</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/admin.js'])
    
    @stack('styles')
</head>
<body class="bg-gray-100 font-sans antialiased">
    <div class="min-h-screen flex" id="admin-layout">
        <!-- Sidebar -->
        @include('components.admin.sidebar')

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header -->
            @include('components.admin.header')

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <div class="container mx-auto px-6 py-8">
                    <!-- Page Header -->
                    @if(View::hasSection('page-header'))
                        <div class="mb-6">
                            @yield('page-header')
                        </div>
                    @elseif(isset($pageTitle))
                        <div class="mb-6">
                            <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
                            @isset($pageDescription)
                                <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
                            @endisset
                        </div>
                    @endif

                    <!-- Alerts -->
                    @if(session('success'))
                        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    <!-- Page Content -->
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    @stack('scripts')

    <!-- Admin Layout JavaScript -->
    <script>
        // Admin layout functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar state management
            let sidebarOpen = false;

            // Get elements
            const sidebar = document.getElementById('admin-sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const sidebarCloseBtn = document.getElementById('sidebar-close-btn');

            // Toggle sidebar function
            function toggleSidebar() {
                sidebarOpen = !sidebarOpen;
                updateSidebarState();
            }

            // Update sidebar state
            function updateSidebarState() {
                if (sidebarOpen) {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('hidden');
                        sidebarOverlay.classList.add('opacity-100');
                    }
                } else {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.add('hidden');
                        sidebarOverlay.classList.remove('opacity-100');
                    }
                }
            }

            // Event listeners
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', toggleSidebar);
            }

            if (sidebarCloseBtn) {
                sidebarCloseBtn.addEventListener('click', toggleSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', toggleSidebar);
            }

            // Dropdown functionality
            const dropdowns = document.querySelectorAll('[data-dropdown]');
            dropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('[data-dropdown-button]');
                const menu = dropdown.querySelector('[data-dropdown-menu]');
                const icon = dropdown.querySelector('[data-dropdown-icon]');

                if (button && menu) {
                    let isOpen = dropdown.dataset.dropdown === 'true';

                    // Set initial state
                    if (isOpen) {
                        menu.classList.remove('hidden');
                        if (icon) icon.classList.add('rotate-180');
                    } else {
                        menu.classList.add('hidden');
                        if (icon) icon.classList.remove('rotate-180');
                    }

                    button.addEventListener('click', function() {
                        isOpen = !isOpen;

                        if (isOpen) {
                            menu.classList.remove('hidden');
                            if (icon) icon.classList.add('rotate-180');
                        } else {
                            menu.classList.add('hidden');
                            if (icon) icon.classList.remove('rotate-180');
                        }
                    });
                }
            });

            // Header dropdown functionality
            const headerDropdowns = document.querySelectorAll('[data-header-dropdown]');
            headerDropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('[data-header-dropdown-button]');
                const menu = dropdown.querySelector('[data-header-dropdown-menu]');

                if (button && menu) {
                    let isOpen = false;

                    button.addEventListener('click', function(e) {
                        e.stopPropagation();
                        isOpen = !isOpen;

                        if (isOpen) {
                            menu.classList.remove('hidden');
                        } else {
                            menu.classList.add('hidden');
                        }
                    });

                    // Close on outside click
                    document.addEventListener('click', function() {
                        if (isOpen) {
                            isOpen = false;
                            menu.classList.add('hidden');
                        }
                    });
                }
            });

            // Update pending news count in sidebar
            function updatePendingNewsCount() {
                fetch('{{ route("admin.pending-news.stats") }}')
                    .then(response => response.json())
                    .then(data => {
                        const badge = document.getElementById('pending-count-badge');
                        if (badge && data.pending > 0) {
                            badge.textContent = data.pending;
                            badge.classList.remove('hidden');
                        } else if (badge) {
                            badge.classList.add('hidden');
                        }
                    })
                    .catch(error => {
                        console.log('Failed to fetch pending news count:', error);
                    });
            }

            // Update pending count on page load
            // updatePendingNewsCount();

            // Update pending count every 30 seconds
            // setInterval(updatePendingNewsCount, 30000);
        });
    </script>
</body>
</html>
