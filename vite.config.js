import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import compression from 'vite-plugin-compression'

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/js/admin.js',
                'resources/js/utils/common.js'
            ],
            refresh: true,
        }),
        tailwindcss(),
        compression({
            algorithm: 'brotliCompress', // or 'gzip'
            ext: '.br',
        })
    ],
});


