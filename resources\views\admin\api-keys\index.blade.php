@extends('layouts.admin')

@section('title', 'API Keys Management')

@section('content')
<!-- <PERSON> Header -->
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8">
    <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">API Keys Management</h1>
        <p class="text-gray-600">Manage your API keys for various services like Gemini, OpenAI, etc.</p>
    </div>
    <div class="flex items-center space-x-3 mt-4 sm:mt-0">
        <button id="refresh-stats" class="material-button material-button-secondary" title="Refresh Statistics">
            <i class="material-icons text-sm mr-2">refresh</i>
            Refresh Stats
        </button>
        <a href="{{ route('admin.api-keys.create') }}"
           class="material-button material-button-primary">
            <i class="material-icons text-sm mr-2">add</i>
            Add New API Key
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="material-icons text-blue-600">vpn_key</i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Keys</p>
                <p class="text-2xl font-bold text-gray-900" id="total-keys">-</p>
            </div>
        </div>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="material-icons text-green-600">check_circle</i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Active Keys</p>
                <p class="text-2xl font-bold text-gray-900" id="active-keys">-</p>
            </div>
        </div>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="material-icons text-orange-600">today</i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Today's Usage</p>
                <p class="text-2xl font-bold text-gray-900" id="today-usage">-</p>
            </div>
        </div>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="material-icons text-purple-600">analytics</i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Usage</p>
                <p class="text-2xl font-bold text-gray-900" id="total-usage">-</p>
            </div>
        </div>
    </div>
</div>

<!-- API Keys DataTable -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- DataTable Container -->
    <div class="p-6">
        <!-- Custom Controls -->
        <div class="mb-6 flex justify-end">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="material-icons text-gray-400 text-sm">search</i>
                    </div>
                    <input type="text" id="search-input" placeholder="Search API keys..."
                           class="material-input pl-10 w-64">
                </div>
                <button id="refresh-table" class="material-button material-button-sm material-button-secondary p-2" title="Refresh">
                    <i class="material-icons text-sm">refresh</i>
                </button>
            </div>
        </div>

        <!-- DataTable -->
        <div class="overflow-x-auto">
            <table id="api-keys-table" class="w-full">
                <thead class="bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Provider</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Today's Usage</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total Usage</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Currently Used</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTables will populate this -->
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                    <span class="text-sm text-gray-700">Show</span>
                    <select id="page-length" class="w-16">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-sm text-gray-700">entries</span>
                </div>
                <div id="table-info" class="text-sm text-gray-700 mb-4 sm:mb-0"></div>
                <div id="api-keys-pagination" class="w-full sm:w-auto"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<style>
.action-btn.set-active {
    background-color: #10b981;
    color: white;
}
.action-btn.set-active:hover {
    background-color: #059669;
}
</style>
@endpush

@push('scripts')
<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    let table = $('#api-keys-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("admin.api-keys.index") }}',
            type: 'GET'
        },
        columns: [
            {
                data: 'api_info',
                name: 'provider',
                orderable: true,
                searchable: true
            },
            {
                data: 'owner_info',
                name: 'owner',
                orderable: true,
                searchable: true
            },
            {
                data: 'status',
                name: 'status',
                orderable: true,
                searchable: false
            },
            {
                data: 'usage',
                name: 'today_count',
                orderable: true,
                searchable: false,
                className: 'text-center'
            },
            {
                data: 'total_usage',
                name: 'total_count',
                orderable: true,
                searchable: false,
                className: 'text-center'
            },
            {
                data: 'is_active_badge',
                name: 'is_active',
                orderable: true,
                searchable: false,
                className: 'text-center'
            },
            {
                data: 'created_at',
                name: 'created_at',
                orderable: true,
                searchable: false
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-right'
            }
        ],
        order: [[5, 'desc']], // Order by created_at desc
        pageLength: 10,
        language: {
            processing: "Loading...",
            search: "",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries"
        },
        dom: 'rtip', // Show table, processing, info, and pagination
        responsive: true,
        preDrawCallback: function() {
            // Add processing class to wrapper when processing
            if ($('.dataTables_processing').css('display') !== 'none') {
                $('.dataTables_wrapper').addClass('processing');
            }
        },
        drawCallback: function() {
            // Remove processing class when done
            $('.dataTables_wrapper').removeClass('processing');
        },
        initComplete: function() {
            // Update stats only on initial load
            updateStats();
        }
    });

    // Custom search input
    $('#search-input').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Custom page length selector
    $('#page-length').on('change', function() {
        table.page.len(this.value).draw();
    });

    // Refresh button
    $('#refresh-table').on('click', function() {
        table.ajax.reload();
    });

    // Refresh stats button
    $('#refresh-stats').on('click', function() {
        updateStats();
    });

    // Handle delete action
    $(document).on('click', '.action-btn.delete', function(e) {
        e.preventDefault();

        let deleteUrl = $(this).closest('form').attr('action');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: deleteUrl,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Deleted!', response.message, 'success');
                            table.ajax.reload();
                            // Update stats to reflect the deletion
                            updateStats();
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Something went wrong.', 'error');
                    }
                });
            }
        });
    });

    // Update stats function
    function updateStats() {
        $.get('{{ route("admin.api-keys.stats") }}', function(data) {
            $('#total-keys').text(data.total_keys.toLocaleString());
            $('#active-keys').text(data.active_keys.toLocaleString());
            $('#today-usage').text(data.today_usage.toLocaleString());
            $('#total-usage').text(data.total_usage.toLocaleString());
        }).fail(function() {
            // Fallback values in case of error
            $('#total-keys').text('-');
            $('#active-keys').text('-');
            $('#today-usage').text('-');
            $('#total-usage').text('-');
        });
    }

    // Note: Stats are now updated via initComplete callback and manual refresh
});

// Set as active function (global scope for onclick)
function setAsActive(apiKeyId, setActiveUrl) {
    Swal.fire({
        title: 'Set as Active Key?',
        text: 'This will deactivate other keys for the same provider.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3b82f6',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, set as active'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: setActiveUrl,
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Success!', response.message, 'success');
                        // Reload the DataTable
                        $('#api-keys-table').DataTable().ajax.reload();
                        // Update stats to reflect changes
                        updateStats();
                    } else {
                        Swal.fire('Error!', response.message || 'Something went wrong.', 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Something went wrong.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    Swal.fire('Error!', errorMessage, 'error');
                }
            });
        }
    });
}
</script>
@endpush
