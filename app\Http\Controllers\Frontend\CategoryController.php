<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    public function show($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();
        $news = $category->news()->published()->latest()->paginate(12);

        $meta_title = $category->name . ' | ' . config('app.name') ?? $category->title . ' | ' . config('app.name');
        $meta_description = $category->meta_description ?? Str::limit(strip_tags($category->content), 160);
        $meta_keywords = $category->meta_keywords ?? '';
        $canonical = url($category->slug);
        $og_title = $category->og_title ?? $meta_title;
        $og_description = $category->og_description ?? $meta_description;
        $og_type = $category->og_type ?? 'article';
        $meta_image = $category->og_image
            ? asset('storage/' . $category->og_image)
            : asset('storage/' . $category->main_image);
        $published_at = $category->published_at ? $category->published_at->format('Y-m-d\TH:i:sP') : null;
        $updated_at = $category->updated_at ? $category->updated_at->format('Y-m-d\TH:i:sP') : null;
        $robots_meta = $category->robots_meta ?? 'index,follow';
        return view('frontend.categories.show', compact(
            'category',
            'news',
            'meta_title',
            'meta_description',
            'meta_keywords',
            'meta_image',
            'canonical',
            'og_title',
            'og_description',
            'og_type',
            'robots_meta',
            'published_at',
            'updated_at'
        ));
    }
}