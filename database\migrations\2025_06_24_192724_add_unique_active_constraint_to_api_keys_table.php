<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add a computed column that is NULL when is_active is false, and provider when is_active is true
        // This allows multiple NULL values (inactive keys) but only one unique provider value (active key)
        DB::statement('ALTER TABLE api_keys ADD COLUMN active_provider VARCHAR(255) GENERATED ALWAYS AS (CASE WHEN is_active = 1 THEN provider ELSE NULL END) STORED');

        // Add unique constraint on the computed column
        DB::statement('ALTER TABLE api_keys ADD CONSTRAINT api_keys_active_provider_unique UNIQUE (active_provider)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the unique constraint and computed column
        DB::statement('ALTER TABLE api_keys DROP CONSTRAINT IF EXISTS api_keys_active_provider_unique');
        DB::statement('ALTER TABLE api_keys DROP COLUMN IF EXISTS active_provider');
    }
};
