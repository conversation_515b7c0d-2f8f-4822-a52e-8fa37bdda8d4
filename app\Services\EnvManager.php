<?php

namespace App\Services;

use Illuminate\Support\Facades\File;

class EnvManager
{
    /**
     * Update or add an environment variable in the .env file
     *
     * @param string $key
     * @param string|null $value
     * @return bool
     */
    public static function updateEnv($key, $value = null)
    {
        $envPath = base_path('.env');
        
        if (!File::exists($envPath)) {
            return false;
        }

        $envContent = File::get($envPath);
        $lines = explode("\n", $envContent);
        $keyFound = false;
        $newLines = [];

        // Process each line
        foreach ($lines as $line) {
            // Skip empty lines and comments at the beginning
            if (empty(trim($line)) || str_starts_with(trim($line), '#')) {
                $newLines[] = $line;
                continue;
            }

            // Check if this line contains our key
            if (str_starts_with($line, $key . '=')) {
                // Update the existing key
                $newLines[] = $key . '=' . static::formatValue($value);
                $keyFound = true;
            } else {
                $newLines[] = $line;
            }
        }

        // If key wasn't found, add it at the end
        if (!$keyFound) {
            $newLines[] = $key . '=' . static::formatValue($value);
        }

        // Write back to file
        $newContent = implode("\n", $newLines);
        
        try {
            File::put($envPath, $newContent);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Format the value for .env file
     *
     * @param string|null $value
     * @return string
     */
    private static function formatValue($value)
    {
        if ($value === null) {
            return '';
        }

        // If value contains spaces or special characters, wrap in quotes
        if (preg_match('/\s|[#"\'\\\\]/', $value)) {
            return '"' . str_replace('"', '\\"', $value) . '"';
        }

        return $value;
    }

    /**
     * Get current value from .env file
     *
     * @param string $key
     * @return string|null
     */
    public static function getEnvValue($key)
    {
        $envPath = base_path('.env');
        
        if (!File::exists($envPath)) {
            return null;
        }

        $envContent = File::get($envPath);
        $lines = explode("\n", $envContent);

        foreach ($lines as $line) {
            if (str_starts_with($line, $key . '=')) {
                $value = substr($line, strlen($key . '='));
                
                // Remove quotes if present
                if ((str_starts_with($value, '"') && str_ends_with($value, '"')) ||
                    (str_starts_with($value, "'") && str_ends_with($value, "'"))) {
                    $value = substr($value, 1, -1);
                }
                
                return $value;
            }
        }

        return null;
    }

    /**
     * Check if a key exists in .env file
     *
     * @param string $key
     * @return bool
     */
    public static function hasEnvKey($key)
    {
        $envPath = base_path('.env');
        
        if (!File::exists($envPath)) {
            return false;
        }

        $envContent = File::get($envPath);
        return str_contains($envContent, $key . '=');
    }

    /**
     * Remove a key from .env file
     *
     * @param string $key
     * @return bool
     */
    public static function removeEnvKey($key)
    {
        $envPath = base_path('.env');
        
        if (!File::exists($envPath)) {
            return false;
        }

        $envContent = File::get($envPath);
        $lines = explode("\n", $envContent);
        $newLines = [];

        foreach ($lines as $line) {
            if (!str_starts_with($line, $key . '=')) {
                $newLines[] = $line;
            }
        }

        $newContent = implode("\n", $newLines);
        
        try {
            File::put($envPath, $newContent);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Update multiple environment variables at once
     *
     * @param array $variables
     * @return bool
     */
    public static function updateMultipleEnv(array $variables)
    {
        $envPath = base_path('.env');
        
        if (!File::exists($envPath)) {
            return false;
        }

        $envContent = File::get($envPath);
        $lines = explode("\n", $envContent);
        $newLines = [];
        $keysFound = [];

        // Process each line
        foreach ($lines as $line) {
            $lineProcessed = false;
            
            // Skip empty lines and comments
            if (empty(trim($line)) || str_starts_with(trim($line), '#')) {
                $newLines[] = $line;
                continue;
            }

            // Check if this line contains any of our keys
            foreach ($variables as $key => $value) {
                if (str_starts_with($line, $key . '=')) {
                    $newLines[] = $key . '=' . static::formatValue($value);
                    $keysFound[] = $key;
                    $lineProcessed = true;
                    break;
                }
            }

            if (!$lineProcessed) {
                $newLines[] = $line;
            }
        }

        // Add any keys that weren't found
        foreach ($variables as $key => $value) {
            if (!in_array($key, $keysFound)) {
                $newLines[] = $key . '=' . static::formatValue($value);
            }
        }

        // Write back to file
        $newContent = implode("\n", $newLines);
        
        try {
            File::put($envPath, $newContent);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
