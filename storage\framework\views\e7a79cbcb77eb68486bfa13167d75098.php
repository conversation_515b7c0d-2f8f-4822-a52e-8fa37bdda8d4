<?php $__env->startSection('title', 'Users'); ?>

<?php
    $pageTitle = 'Users Management';
    $pageDescription = 'Manage all users in the system';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Users', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <div class="mt-4 sm:mt-0">
        <a href="<?php echo e(route('admin.users.create')); ?>" class="material-button material-button-md material-button-primary flex items-center">
            <i class="material-icons mr-2">add</i>
            Add New User
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Users DataTable -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- DataTable Container -->
    <div class="p-6">
        <!-- Custom Controls -->
        <div class="mb-6 flex justify-end">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="material-icons text-gray-400 text-sm">search</i>
                    </div>
                    <input type="text" id="search-input" placeholder="Search users..."
                           class="material-input pl-10 w-64">
                </div>
                <button id="refresh-table" class="material-button material-button-sm material-button-secondary p-2" title="Refresh">
                    <i class="material-icons text-sm">refresh</i>
                </button>
            </div>
        </div>

        <!-- Table -->
        <div>
            <table id="users-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTables will populate this -->
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                    <span class="text-sm text-gray-700">Show</span>
                    <select id="page-length" class="w-16">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-sm text-gray-700">entries</span>
                </div>
                <div id="table-info" class="text-sm text-gray-700 mb-4 sm:mb-0"></div>
                <div id="users-pagination" class="w-full sm:w-auto"></div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    let table = $('#users-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '<?php echo e(route("admin.users.index")); ?>',
            type: 'GET'
        },
        columns: [
            {
                data: 'user',
                name: 'name',
                orderable: true,
                searchable: true
            },
            {
                data: 'email',
                name: 'email',
                orderable: true,
                searchable: true
            },
            {
                data: 'status',
                name: 'email_verified_at',
                orderable: true,
                searchable: false
            },
            {
                data: 'role',
                name: 'role',
                orderable: false,
                searchable: false
            },
            {
                data: 'created_at',
                name: 'created_at',
                orderable: true,
                searchable: false
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-right'
            }
        ],
        order: [[4, 'desc']], // Sort by created_at desc by default
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div>Loading data...</div>',
            paginate: {
                previous: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-left"><polyline points="15 18 9 12 15 6"></polyline></svg>',
                next: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg>'
            },
            info: "Showing _START_ to _END_ of _TOTAL_ entries"
        },
        dom: 'rtip', // Show table, processing, info, and pagination
        responsive: true,
        preDrawCallback: function() {
            // Add processing class to wrapper when processing
            if ($('.dataTables_processing').css('display') !== 'none') {
                $('.dataTables_wrapper').addClass('processing');
            }
        },
        drawCallback: function(settings) {
            // Remove processing class from wrapper when done
            $('.dataTables_wrapper').removeClass('processing');
            
            // Move pagination to custom container while preserving events
            const $pagination = $('.dataTables_paginate').detach();
            $('#users-pagination').empty().append($pagination);

            // Move info to custom container
            const $info = $('.dataTables_info').detach();
            $('#table-info').empty().append($info);
            
            // Set the current page length in the dropdown
            $('#page-length').val(table.page.len());
            
            // Add custom class to pagination buttons
            $('.paginate_button').addClass('custom-paginate-button');
            
            // Ensure SVG icons are properly centered
            $('.paginate_button svg').parent().css({
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center'
            });
        }
    });

    // Custom search functionality
    $('#search-input').on('keyup', function() {
        table.search($(this).val()).draw();
    });

    // Custom page length functionality
    $('#page-length').on('change', function() {
        table.page.len(parseInt($(this).val())).draw();
    });

    // Refresh table functionality
    $('#refresh-table').on('click', function() {
        table.ajax.reload();
    });

    // Handle delete confirmation with SweetAlert2
    $(document).on('submit', '.delete-form', function(e) {
        e.preventDefault();
        const form = this;
        
        Swal.fire({
            title: 'Are you sure?',
            text: "You are about to delete this user. This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

    // Custom event handler for processing state
    $(document).ajaxStart(function() {
        $('.dataTables_wrapper').addClass('processing');
    }).ajaxStop(function() {
        $('.dataTables_wrapper').removeClass('processing');
    });

    // Additional event handler for DataTables processing
    table.on('processing.dt', function(e, settings, processing) {
        if (processing) {
            $('.dataTables_wrapper').addClass('processing');
        } else {
            $('.dataTables_wrapper').removeClass('processing');
        }
    });
});
</script>
<?php $__env->stopPush(); ?>





<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/users/index.blade.php ENDPATH**/ ?>