<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('api_keys', function (Blueprint $table) {
            // Add the new owner column
            $table->string('owner')->after('id');

            // Rename author column to provider
            $table->renameColumn('author', 'provider');

            // Update indexes
            $table->dropIndex(['author', 'status']);
            $table->index(['provider', 'status']);
            $table->index(['owner']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('api_keys', function (Blueprint $table) {
            // Remove the owner column
            $table->dropColumn('owner');

            // Rename provider column back to author
            $table->renameColumn('provider', 'author');

            // Restore original indexes
            $table->dropIndex(['provider', 'status']);
            $table->dropIndex(['owner']);
            $table->index(['author', 'status']);
        });
    }
};
